#!/usr/bin/env node

/**
 * Test script for Remote Browser Streaming System
 * Tests both Variant 1 and Variant 2 functionality
 */

import { spawn } from 'child_process';
import WebSocket from 'ws';
import { setTimeout } from 'timers/promises';

class StreamingTester {
    constructor() {
        this.serverProcess = null;
        this.testResults = {
            variant1: { passed: false, errors: [] },
            variant2: { passed: false, errors: [] }
        };
    }

    async runTests() {
        console.log('🧪 Starting Remote Browser Streaming Tests\n');

        try {
            // Test Variant 1
            await this.testVariant(1);
            
            // Test Variant 2
            await this.testVariant(2);

            // Print results
            this.printResults();

        } catch (error) {
            console.error('❌ Test suite failed:', error.message);
        }
    }

    async testVariant(variant) {
        console.log(`\n=== Testing Variant ${variant} ===`);
        
        try {
            // Start server with specific variant
            await this.startServer(variant);
            
            // Wait for server to be ready
            await setTimeout(2000);
            
            // Test WebSocket connection
            await this.testWebSocketConnection(variant);
            
            // Test stream request
            await this.testStreamRequest(variant);
            
            // Mark as passed
            this.testResults[`variant${variant}`].passed = true;
            console.log(`✅ Variant ${variant} tests passed`);
            
        } catch (error) {
            console.error(`❌ Variant ${variant} test failed:`, error.message);
            this.testResults[`variant${variant}`].errors.push(error.message);
        } finally {
            await this.stopServer();
        }
    }

    async startServer(variant) {
        console.log(`🚀 Starting server with Variant ${variant}...`);
        
        return new Promise((resolve, reject) => {
            const env = { ...process.env, VARIANT: variant.toString() };
            this.serverProcess = spawn('node', ['server.js'], { 
                env,
                stdio: 'pipe'
            });

            let output = '';
            
            this.serverProcess.stdout.on('data', (data) => {
                output += data.toString();
                if (output.includes('WebSocket server ready for connections')) {
                    resolve();
                }
            });

            this.serverProcess.stderr.on('data', (data) => {
                console.error('Server error:', data.toString());
            });

            this.serverProcess.on('error', (error) => {
                reject(new Error(`Failed to start server: ${error.message}`));
            });

            // Timeout after 10 seconds
            setTimeout(() => {
                if (this.serverProcess && !this.serverProcess.killed) {
                    reject(new Error('Server startup timeout'));
                }
            }, 10000);
        });
    }

    async stopServer() {
        if (this.serverProcess && !this.serverProcess.killed) {
            console.log('🛑 Stopping server...');
            this.serverProcess.kill('SIGINT');
            
            // Wait for process to exit
            await new Promise((resolve) => {
                this.serverProcess.on('exit', resolve);
                setTimeout(resolve, 2000); // Fallback timeout
            });
        }
    }

    async testWebSocketConnection(variant) {
        console.log('🔌 Testing WebSocket connection...');
        
        return new Promise((resolve, reject) => {
            const ws = new WebSocket('ws://localhost:8080');
            let connected = false;
            
            ws.on('open', () => {
                console.log('✅ WebSocket connected');
                connected = true;
                ws.close();
                resolve();
            });

            ws.on('message', (data) => {
                const message = JSON.parse(data.toString());
                console.log('📨 Received message:', message.type);
                
                if (message.type === 'connected') {
                    console.log(`✅ Server confirmed connection with Variant ${message.variant}`);
                    if (message.variant !== variant) {
                        reject(new Error(`Expected variant ${variant}, got ${message.variant}`));
                    }
                }
            });

            ws.on('error', (error) => {
                reject(new Error(`WebSocket connection failed: ${error.message}`));
            });

            ws.on('close', () => {
                if (connected) {
                    resolve();
                } else {
                    reject(new Error('WebSocket closed before connection established'));
                }
            });

            // Timeout after 5 seconds
            setTimeout(() => {
                if (!connected) {
                    ws.close();
                    reject(new Error('WebSocket connection timeout'));
                }
            }, 5000);
        });
    }

    async testStreamRequest(variant) {
        console.log('🎥 Testing stream request...');
        
        return new Promise((resolve, reject) => {
            const ws = new WebSocket('ws://localhost:8080');
            let streamRequested = false;
            let streamStarting = false;
            
            ws.on('open', () => {
                console.log('📡 Requesting stream...');
                ws.send(JSON.stringify({ type: 'request-stream' }));
                streamRequested = true;
            });

            ws.on('message', (data) => {
                const message = JSON.parse(data.toString());
                console.log('📨 Stream message:', message.type);
                
                if (message.type === 'stream-starting') {
                    console.log('✅ Stream starting confirmed');
                    streamStarting = true;
                    
                    // Wait a bit for stream to initialize, then close
                    setTimeout(() => {
                        ws.close();
                        resolve();
                    }, 3000);
                }
                
                if (message.type === 'error') {
                    reject(new Error(`Stream error: ${message.message}`));
                }
            });

            ws.on('error', (error) => {
                reject(new Error(`Stream test failed: ${error.message}`));
            });

            ws.on('close', () => {
                if (streamRequested && streamStarting) {
                    resolve();
                } else if (!streamRequested) {
                    reject(new Error('Failed to request stream'));
                } else {
                    reject(new Error('Stream did not start'));
                }
            });

            // Timeout after 15 seconds (streaming can take time)
            setTimeout(() => {
                if (!streamStarting) {
                    ws.close();
                    reject(new Error('Stream request timeout'));
                }
            }, 15000);
        });
    }

    printResults() {
        console.log('\n' + '='.repeat(50));
        console.log('🧪 TEST RESULTS');
        console.log('='.repeat(50));
        
        for (const [variant, result] of Object.entries(this.testResults)) {
            const variantNum = variant.replace('variant', '');
            const status = result.passed ? '✅ PASSED' : '❌ FAILED';
            
            console.log(`\nVariant ${variantNum}: ${status}`);
            
            if (result.errors.length > 0) {
                console.log('  Errors:');
                result.errors.forEach(error => {
                    console.log(`    - ${error}`);
                });
            }
        }
        
        const totalPassed = Object.values(this.testResults).filter(r => r.passed).length;
        const totalTests = Object.keys(this.testResults).length;
        
        console.log(`\n📊 Summary: ${totalPassed}/${totalTests} tests passed`);
        
        if (totalPassed === totalTests) {
            console.log('🎉 All tests passed! The streaming system is working correctly.');
        } else {
            console.log('⚠️  Some tests failed. Check the errors above.');
        }
        
        console.log('='.repeat(50));
    }
}

// Run tests if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
    const tester = new StreamingTester();
    
    process.on('SIGINT', async () => {
        console.log('\n🛑 Test interrupted. Cleaning up...');
        await tester.stopServer();
        process.exit(0);
    });
    
    tester.runTests().catch(console.error);
}

export { StreamingTester };
