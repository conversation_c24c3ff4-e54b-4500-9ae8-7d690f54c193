/**
 * Simple CDP Script - Open 2 Tabs & Start Screen Sharing in Tab 1
 *
 * Run Chrome with:
 * chrome --remote-debugging-port=9222 \
 *   --no-first-run --no-default-browser-check \
 *   --auto-select-desktop-capture-source="Entire screen" \
 *   --use-fake-ui-for-media-stream
 */

import { createTarget, CDP } from "simple-cdp";
import WebSocket from "ws";

// Polyfill WebSocket for Node.js
global.WebSocket = WebSocket;

async function main() {
  console.log("📍 Opening two tabs at google.com...");

  // Tab 1
  const targetInfo1 = await createTarget("https://www.google.com");
  const tab1 = new CDP(targetInfo1);
  await tab1.Runtime.enable();
  await tab1.Page.enable();

  // Tab 2
  const targetInfo2 = await createTarget("https://www.google.com");
  const tab2 = new CDP(targetInfo2);
  await tab2.Runtime.enable();
  await tab2.Page.enable();

  console.log("✅ Tabs opened.");

  // Small delay to ensure pages are ready
  await new Promise((resolve) => setTimeout(resolve, 2000));

  // Inject screen share script into Tab 1
  console.log("🎥 Injecting getDisplayMedia script into Tab 1...");

  const result = await tab1.Runtime.evaluate({
    expression: `
      (async () => {
        try {
          const stream = await navigator.mediaDevices.getDisplayMedia({
            video: { frameRate: 25 },
            audio: false
          });

          // Attach to video element for visibility
          const video = document.createElement("video");
          video.srcObject = stream;
          video.autoplay = true;
          video.style.position = "fixed";
          video.style.bottom = "10px";
          video.style.right = "10px";
          video.style.width = "200px";
          video.style.zIndex = "9999";
          document.body.appendChild(video);

          return "✅ Screen sharing started";
        } catch (err) {
          return "❌ Error: " + err.message;
        }
      })();
    `,
    awaitPromise: true,
    returnByValue: true,
  });

  console.log("Injection result (Tab 1):", result.result.value);

  // Example: check both tab titles
  const title1 = await tab1.Runtime.evaluate({ expression: "document.title" });
  const title2 = await tab2.Runtime.evaluate({ expression: "document.title" });

  console.log("📄 Tab 1 title:", title1.result.value);
  console.log("📄 Tab 2 title:", title2.result.value);
}

main().catch((err) => {
  console.error("❌ Error:", err);
});
