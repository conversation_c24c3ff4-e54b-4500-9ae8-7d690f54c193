import { EventEmitter } from "events";
import puppeteer from "puppeteer";
import { CDP } from "simple-cdp";

/**
 * Variant 2: Uses Chrome DevTools Protocol (CDP) Page.startScreencast
 * This variant connects to Chrome via CDP and uses the screencast API
 * to capture frames and convert them to a video stream
 */
export class Variant2 extends EventEmitter {
  constructor(config = {}) {
    super();
    this.config = {
      headless: false, // Can be true for CDP variant
      width: config.width || 1920,
      height: config.height || 1080,
      quality: config.quality || 80, // JPEG quality 1-100
      format: config.format || "jpeg", // 'jpeg' or 'png'
      everyNthFrame: config.everyNthFrame || 1, // Capture every Nth frame
      ...config,
    };

    this.browser = null;
    this.page = null;
    this.cdpSession = null;
    this.isStreaming = false;
    this.frameCount = 0;
    this.frames = [];
  }

  /**
   * Launch Chrome browser
   */
  async launchBrowser() {
    console.log("Launching Chrome for CDP screencast...");

    this.browser = await puppeteer.launch({
      headless: this.config.headless,
      args: [
        "--no-sandbox",
        "--auto-select-desktop-capture-source=Entire screen",
        "--disable-web-security",
        "--remote-debugging-port=0", // Let Puppeteer choose the port
        "--auto-accept-this-tab-capture",
      ],
      defaultViewport: {
        width: this.config.width,
        height: this.config.height,
      },
    });

    this.page = await this.browser.newPage();

    // Navigate to a test page
    await this.page.goto("https://example.com");

    console.log("Browser launched successfully");
  }

  /**
   * Setup CDP connection and screencast
   */
  async setupCDPScreencast() {
    console.log("Setting up CDP screencast...");

    // Get the CDP session from Puppeteer
    this.cdpSession = await this.page.target().createCDPSession();

    // Enable Page domain
    await this.cdpSession.send("Page.enable");

    // Set up screencast frame handler
    this.cdpSession.on("Page.screencastFrame", (params) => {
      this.handleScreencastFrame(params);
    });

    // Start screencast
    const screencastParams = {
      format: this.config.format,
      quality: this.config.quality,
      maxWidth: this.config.width,
      maxHeight: this.config.height,
      everyNthFrame: this.config.everyNthFrame,
    };

    console.log("Starting screencast with params:", screencastParams);
    await this.cdpSession.send("Page.startScreencast", screencastParams);

    console.log("CDP screencast started successfully");
  }

  /**
   * Handle incoming screencast frames
   */
  handleScreencastFrame(params) {
    this.frameCount++;

    // Acknowledge the frame (required by CDP)
    this.cdpSession
      .send("Page.screencastFrameAck", {
        sessionId: params.sessionId,
      })
      .catch(console.error);

    // Process the frame
    const frameData = {
      sessionId: params.sessionId,
      data: params.data, // Base64 encoded image
      metadata: params.metadata,
      timestamp: Date.now(),
      frameNumber: this.frameCount,
    };

    // Store recent frames (keep last 10 for debugging)
    this.frames.push(frameData);
    if (this.frames.length > 10) {
      this.frames.shift();
    }

    // Emit frame event
    this.emit("frame", frameData);

    // Log every 30 frames to avoid spam
    if (this.frameCount % 30 === 0) {
      console.log(
        `Captured ${this.frameCount} frames, latest: ${frameData.metadata.deviceWidth}x${frameData.metadata.deviceHeight}`
      );
    }
  }

  /**
   * Convert base64 frame to blob (for WebRTC integration)
   */
  frameToBlob(frameData) {
    try {
      // Convert base64 to binary
      const binaryString = atob(frameData.data);
      const bytes = new Uint8Array(binaryString.length);

      for (let i = 0; i < binaryString.length; i++) {
        bytes[i] = binaryString.charCodeAt(i);
      }

      // Create blob
      const mimeType =
        this.config.format === "jpeg" ? "image/jpeg" : "image/png";
      return new Blob([bytes], { type: mimeType });
    } catch (error) {
      console.error("Error converting frame to blob:", error);
      return null;
    }
  }

  /**
   * Get the latest frame as base64
   */
  getLatestFrame() {
    return this.frames[this.frames.length - 1] || null;
  }

  /**
   * Get frame statistics
   */
  getFrameStats() {
    return {
      totalFrames: this.frameCount,
      recentFrames: this.frames.length,
      isStreaming: this.isStreaming,
      config: this.config,
    };
  }

  /**
   * Start the streaming process
   */
  async startStreaming() {
    if (this.isStreaming) {
      console.log("Already streaming");
      return;
    }

    try {
      console.log("Starting Variant 2 streaming (CDP screencast)...");

      await this.launchBrowser();
      await this.setupCDPScreencast();

      this.isStreaming = true;

      // Emit stream ready event
      this.emit("stream", {
        variant: 2,
        method: "CDP screencast",
        config: this.config,
      });

      console.log("Variant 2 streaming started successfully");
    } catch (error) {
      console.error("Error starting Variant 2 streaming:", error);
      this.emit("error", error);
      throw error;
    }
  }

  /**
   * Stop streaming and cleanup
   */
  async stopStreaming() {
    if (!this.isStreaming) {
      return;
    }

    console.log("Stopping Variant 2 streaming...");

    try {
      // Stop screencast
      if (this.cdpSession) {
        await this.cdpSession.send("Page.stopScreencast");
      }
    } catch (error) {
      console.error("Error stopping screencast:", error);
    }

    this.isStreaming = false;
    console.log("Variant 2 streaming stopped");
  }

  /**
   * Navigate to a different URL (useful for testing)
   */
  async navigateTo(url) {
    if (this.page) {
      console.log(`Navigating to: ${url}`);
      await this.page.goto(url);
    }
  }

  /**
   * Cleanup resources
   */
  async cleanup() {
    console.log("Cleaning up Variant 2...");

    await this.stopStreaming();

    if (this.cdpSession) {
      await this.cdpSession.detach();
      this.cdpSession = null;
    }

    if (this.page) {
      await this.page.close();
      this.page = null;
    }

    if (this.browser) {
      await this.browser.close();
      this.browser = null;
    }

    console.log("Variant 2 cleanup complete");
  }
}
