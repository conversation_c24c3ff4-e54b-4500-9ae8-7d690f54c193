I want you to build a remote browser streaming system with two implementations (variants).

Overall Architecture

A server process:

Launches a remote Chrome instance (headless or non-headless).

Sets up a WebSocket server for communication with clients.

Manages WebRTC signaling via the WebSocket (exchange of SDP offers/answers and ICE candidates).

Provides two ways of capturing the browser screen (Variant 1 and Variant 2).

A client app (can be web-based in plain JS):

Connects to the server over WebSocket.

Uses WebRTC to establish a peer connection with the server.

Receives the video stream from the remote browser and plays it in an HTML <video> element.

(Later: sends user input events back over WebSocket to be injected via CDP).

Variant 1 → Using --auto-select-desktop-capture-source

Start Chrome with flags:

--enable-usermedia-screen-capturing

--auto-select-desktop-capture-source="NAME_OF_SCREEN_OR_WINDOW"

--allow-http-screen-capture

On the server side, inject a script into the browser that calls:

navigator.mediaDevices.getDisplayMedia({ video: true, audio: false })

Capture the returned MediaStream and hook it into WebRTC.

Forward the video stream from the browser process to the client via WebRTC.

Variant 2 → Using CDP Page.startScreencast

Connect to Chrome via Chrome DevTools Protocol (CDP).

Attach to a specific target (tab).

Call Page.startScreencast with settings (jpeg/webp, quality, size).

On Page.screencastFrame events:

Convert the base64 image to a Blob or raw frame.

Feed it into a WebRTC MediaStreamTrack (e.g., using a CanvasCaptureStream pipeline).

Send the track to the client over WebRTC.

Requirements

Provide working server code (Node.js preferred).

Use puppeteer-core or chrome-remote-interface for CDP.

Use a WebSocket library (like ws).

Use a WebRTC library for Node (like wrtc).

Provide client code (vanilla JS + HTML).

Handles WebSocket signaling.

Establishes WebRTC connection.

Plays the received stream in <video autoplay playsinline>.

Support both Variant 1 and Variant 2 behind a configuration flag.

Document how to run:

Install dependencies

Start server

Open client page

Stream should appear in client.

👉 Please generate complete, runnable code for both the server and client for both variants.
👉 Keep the architecture clean so it’s easy to switch between Variant 1 and Variant 2.
