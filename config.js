/**
 * Configuration file for Remote Browser Streaming System
 */

export const config = {
    // Server configuration
    server: {
        port: process.env.PORT || 8080,
        host: process.env.HOST || 'localhost'
    },
    
    // Variant selection (1 or 2)
    variant: parseInt(process.env.VARIANT) || 1,
    
    // Browser configuration
    browser: {
        headless: process.env.HEADLESS === 'true' || false,
        width: parseInt(process.env.BROWSER_WIDTH) || 1920,
        height: parseInt(process.env.BROWSER_HEIGHT) || 1080
    },
    
    // Variant 1 specific configuration (getDisplayMedia)
    variant1: {
        // Screen capture settings
        screenName: process.env.SCREEN_NAME || null, // Auto-detect if null
        
        // Media constraints
        video: {
            width: { ideal: 1920 },
            height: { ideal: 1080 },
            frameRate: { ideal: 30 }
        },
        audio: false
    },
    
    // Variant 2 specific configuration (CDP screencast)
    variant2: {
        // Screencast settings
        format: process.env.SCREENCAST_FORMAT || 'jpeg', // 'jpeg' or 'png'
        quality: parseInt(process.env.SCREENCAST_QUALITY) || 80, // 1-100
        everyNthFrame: parseInt(process.env.SCREENCAST_EVERY_NTH_FRAME) || 1,
        
        // Target URL for the browser to navigate to
        targetUrl: process.env.TARGET_URL || 'https://example.com'
    },
    
    // WebRTC configuration
    webrtc: {
        iceServers: [
            { urls: 'stun:stun.l.google.com:19302' },
            { urls: 'stun:stun1.l.google.com:19302' }
        ]
    },
    
    // Logging configuration
    logging: {
        level: process.env.LOG_LEVEL || 'info', // 'debug', 'info', 'warn', 'error'
        enableFrameLogging: process.env.ENABLE_FRAME_LOGGING === 'true' || false
    }
};

/**
 * Get configuration for a specific variant
 */
export function getVariantConfig(variant = config.variant) {
    const baseConfig = {
        ...config.browser,
        ...config.webrtc,
        logging: config.logging
    };
    
    if (variant === 1) {
        return {
            ...baseConfig,
            ...config.variant1
        };
    } else if (variant === 2) {
        return {
            ...baseConfig,
            ...config.variant2
        };
    } else {
        throw new Error(`Invalid variant: ${variant}. Use 1 or 2.`);
    }
}

/**
 * Validate configuration
 */
export function validateConfig() {
    const errors = [];
    
    // Validate variant
    if (![1, 2].includes(config.variant)) {
        errors.push(`Invalid variant: ${config.variant}. Must be 1 or 2.`);
    }
    
    // Validate port
    if (config.server.port < 1 || config.server.port > 65535) {
        errors.push(`Invalid port: ${config.server.port}. Must be between 1 and 65535.`);
    }
    
    // Validate browser dimensions
    if (config.browser.width < 100 || config.browser.width > 4000) {
        errors.push(`Invalid browser width: ${config.browser.width}. Must be between 100 and 4000.`);
    }
    
    if (config.browser.height < 100 || config.browser.height > 4000) {
        errors.push(`Invalid browser height: ${config.browser.height}. Must be between 100 and 4000.`);
    }
    
    // Validate variant 2 specific settings
    if (config.variant === 2) {
        if (!['jpeg', 'png'].includes(config.variant2.format)) {
            errors.push(`Invalid screencast format: ${config.variant2.format}. Must be 'jpeg' or 'png'.`);
        }
        
        if (config.variant2.quality < 1 || config.variant2.quality > 100) {
            errors.push(`Invalid screencast quality: ${config.variant2.quality}. Must be between 1 and 100.`);
        }
    }
    
    if (errors.length > 0) {
        throw new Error(`Configuration validation failed:\n${errors.join('\n')}`);
    }
    
    return true;
}

/**
 * Print current configuration
 */
export function printConfig() {
    console.log('=== Remote Browser Streaming Configuration ===');
    console.log(`Server: ${config.server.host}:${config.server.port}`);
    console.log(`Variant: ${config.variant} (${config.variant === 1 ? 'getDisplayMedia' : 'CDP screencast'})`);
    console.log(`Browser: ${config.browser.width}x${config.browser.height} (headless: ${config.browser.headless})`);
    
    if (config.variant === 1) {
        console.log(`Screen Name: ${config.variant1.screenName || 'auto-detect'}`);
        console.log(`Video Settings: ${JSON.stringify(config.variant1.video)}`);
    } else {
        console.log(`Screencast Format: ${config.variant2.format}`);
        console.log(`Screencast Quality: ${config.variant2.quality}`);
        console.log(`Target URL: ${config.variant2.targetUrl}`);
    }
    
    console.log(`Log Level: ${config.logging.level}`);
    console.log('===============================================');
}
