# Remote Browser Streaming System

A Node.js-based system for streaming remote browser content to clients via WebRTC. Supports two different capture methods (variants) for maximum compatibility and performance.

## Architecture Overview

The system consists of:

1. **Server Process** (`server.js`): Manages WebSocket connections, WebRTC signaling, and coordinates the streaming variants
2. **Variant 1** (`variant1.js`): Uses Chrome's `getDisplayMedia` API with auto-select flags for screen capture
3. **Variant 2** (`variant2.js`): Uses Chrome DevTools Protocol (CDP) `Page.startScreencast` for frame capture
4. **Client Application** (`client.html`): Web-based client that connects via WebSocket and displays the stream

## Features

- **Two Capture Methods**: Choose between getDisplayMedia (Variant 1) or CDP screencast (Variant 2)
- **WebSocket Signaling**: Real-time communication between server and clients
- **WebRTC Streaming**: Low-latency video streaming (when WebRTC library is available)
- **Web-based Client**: No additional software required for viewing
- **Configurable**: Extensive configuration options for both variants
- **Cross-platform**: Works on Windows, macOS, and Linux

## Installation

### Prerequisites

- Node.js 16+ 
- Chrome/Chromium browser
- For production use: `wrtc` package (requires compilation)

### Setup

1. **Clone/Download the project files**

2. **Install dependencies**:
   ```bash
   npm install
   ```

3. **Install WebRTC library** (optional, for full WebRTC functionality):
   ```bash
   # May require sudo on some systems
   npm install wrtc
   ```

## Usage

### Quick Start

1. **Start the server with Variant 1** (getDisplayMedia):
   ```bash
   node server.js
   ```

2. **Start the server with Variant 2** (CDP screencast):
   ```bash
   VARIANT=2 node server.js
   ```

3. **Open the client**:
   - Navigate to `http://localhost:8080` in your browser
   - Click "Connect to Server"
   - Click "Request Stream"

### Configuration Options

#### Environment Variables

```bash
# Server configuration
PORT=8080                    # Server port
HOST=localhost              # Server host

# Variant selection
VARIANT=1                   # 1 for getDisplayMedia, 2 for CDP screencast

# Browser settings
HEADLESS=false              # Run browser in headless mode
BROWSER_WIDTH=1920          # Browser viewport width
BROWSER_HEIGHT=1080         # Browser viewport height

# Variant 2 specific (CDP screencast)
SCREENCAST_FORMAT=jpeg      # 'jpeg' or 'png'
SCREENCAST_QUALITY=80       # 1-100 (JPEG quality)
TARGET_URL=https://example.com  # URL to navigate to

# Logging
LOG_LEVEL=info              # debug, info, warn, error
ENABLE_FRAME_LOGGING=false  # Log frame capture details
```

#### Configuration File

Edit `config.js` to customize settings:

```javascript
export const config = {
    variant: 1,  // 1 or 2
    server: { port: 8080 },
    browser: { width: 1920, height: 1080 },
    // ... more options
};
```

### Running Examples

#### Example 1: Basic Variant 1 (getDisplayMedia)
```bash
node server.js
```

#### Example 2: Variant 2 with custom settings
```bash
VARIANT=2 SCREENCAST_QUALITY=90 TARGET_URL=https://github.com node server.js
```

#### Example 3: High-resolution streaming
```bash
BROWSER_WIDTH=2560 BROWSER_HEIGHT=1440 SCREENCAST_QUALITY=95 node server.js
```

## Variants Explained

### Variant 1: getDisplayMedia API

**How it works**:
- Launches Chrome with special flags: `--auto-select-desktop-capture-source`
- Injects JavaScript that calls `navigator.mediaDevices.getDisplayMedia()`
- Captures the entire screen or specific window
- Streams via WebRTC

**Pros**:
- Native browser API
- High performance
- Full screen capture
- Audio support (if enabled)

**Cons**:
- Requires specific Chrome flags
- May need user permission on some systems
- Cannot capture specific browser content only

**Best for**: Full desktop sharing, screen recording applications

### Variant 2: CDP Screencast

**How it works**:
- Connects to Chrome via Chrome DevTools Protocol (CDP)
- Uses `Page.startScreencast` to capture browser frames
- Converts frames to video stream
- Streams via WebRTC

**Pros**:
- Captures only browser content
- No special Chrome flags required
- Frame-by-frame control
- Works in headless mode

**Cons**:
- Lower frame rate than Variant 1
- JPEG/PNG compression artifacts
- No audio capture
- Higher CPU usage

**Best for**: Browser automation, web application streaming, headless environments

## File Structure

```
├── server.js              # Main server with WebSocket and variant coordination
├── variant1.js            # getDisplayMedia implementation
├── variant2.js            # CDP screencast implementation
├── client.html            # Web client application
├── config.js              # Configuration management
├── STREAMING_README.md     # This documentation
└── package.json           # Dependencies
```

## API Reference

### WebSocket Messages

#### Client to Server:
- `request-stream`: Start streaming
- `webrtc-offer`: WebRTC offer for peer connection
- `webrtc-ice-candidate`: ICE candidate for WebRTC
- `ping`: Keep-alive ping

#### Server to Client:
- `connected`: Connection confirmation with client ID and variant
- `stream-starting`: Stream initialization started
- `stream-ready`: Stream is ready for WebRTC connection
- `webrtc-answer`: WebRTC answer response
- `error`: Error message
- `pong`: Ping response

## Troubleshooting

### Common Issues

1. **Permission Denied (Variant 1)**:
   - Ensure Chrome has screen recording permissions
   - Try running with `--no-sandbox` flag
   - Check system privacy settings

2. **WebRTC Connection Failed**:
   - Install the `wrtc` package: `npm install wrtc`
   - Check firewall settings
   - Verify STUN server accessibility

3. **No Frames Captured (Variant 2)**:
   - Ensure target page loads successfully
   - Check CDP connection in browser logs
   - Verify Chrome DevTools Protocol is enabled

4. **High CPU Usage**:
   - Reduce screencast quality (Variant 2)
   - Increase `everyNthFrame` setting
   - Use headless mode if visual output not needed

### Debug Mode

Enable detailed logging:
```bash
LOG_LEVEL=debug ENABLE_FRAME_LOGGING=true node server.js
```

## Performance Tips

1. **For Variant 1**:
   - Use lower resolution for better performance
   - Disable audio if not needed
   - Ensure good screen recording permissions

2. **For Variant 2**:
   - Adjust JPEG quality vs performance trade-off
   - Use `everyNthFrame > 1` for lower frame rates
   - Consider PNG for text-heavy content

## Security Considerations

- The server runs Chrome with reduced security flags
- Only run on trusted networks
- Consider authentication for production use
- Validate all WebSocket messages
- Use HTTPS in production environments

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Test both variants
5. Submit a pull request

## License

MIT License - see LICENSE file for details
