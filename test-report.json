{"timestamp": "2025-09-01T17:03:16.743Z", "summary": {"total_platforms": 3, "tested_platforms": 0, "successful_platforms": 0, "overall_success_rate": 0}, "results": {"github": {"status": "error", "error": "Command failed with exit code 1"}, "google": {"status": "skipped", "reason": "No session found", "recommendation": "Run: node enhanced-session-inject.js google"}, "facebook": {"status": "skipped", "reason": "No session found", "recommendation": "Run: node enhanced-session-inject.js facebook"}}}