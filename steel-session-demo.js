/**
 * Steel SDK Session Persistence Demo
 * Demonstrates session transfer for GitHub and Google using Steel SDK
 */

import { chromium } from "playwright";
import Steel from "steel-sdk";
import dotenv from "dotenv";
import { PLA<PERSON>ORMS, SessionLogger, validateSession } from "./session-utils.js";

dotenv.config();

const client = new Steel({
  steelAPIKey: process.env.STEEL_API_KEY,
});

// Platform configurations for Steel SDK
const STEEL_PLATFORMS = {
  github: {
    ...PLATFORMS.github,
    loginSteps: async (page) => {
      console.log("🔐 Please log in to GitHub manually in the Steel session");
      console.log("📍 Navigate to: https://github.com/login");
      console.log("⏳ Waiting for authentication...");

      // Wait for user to complete login
      await page.goto("https://github.com/login");

      // Wait for successful login by checking for redirect to main page
      await page.waitForURL("https://github.com/", { timeout: 120000 });

      // Additional validation
      await page.waitForSelector('meta[name="user-login"]', { timeout: 10000 });

      console.log("✅ GitHub authentication detected!");
    },
  },
  google: {
    ...PLATFORMS.google,
    loginSteps: async (page) => {
      console.log("🔐 Please log in to Google manually in the Steel session");
      console.log("📍 Navigate to: https://accounts.google.com/signin");
      console.log("⏳ Waiting for authentication...");

      // Wait for user to complete login
      await page.goto("https://accounts.google.com/signin");

      // Wait for successful login by checking for redirect to account page
      await page.waitForURL(
        /myaccount\.google\.com|accounts\.google\.com\/.*authenticated/,
        { timeout: 120000 }
      );

      // Navigate to a page where we can validate login
      await page.goto("https://myaccount.google.com/");

      // Wait for account indicators
      await page.waitForSelector('[aria-label*="Google Account"], .gb_A', {
        timeout: 10000,
      });

      console.log("✅ Google authentication detected!");
    },
  },
};

// Helper function to perform platform-specific login
async function performLogin(page, platform, logger) {
  logger.info(`Starting ${platform} authentication process`);

  const config = STEEL_PLATFORMS[platform];
  if (!config) {
    throw new Error(`Unsupported platform: ${platform}`);
  }

  try {
    await config.loginSteps(page);

    // Validate the login
    const validation = await validateSession(page, platform, logger);

    if (!validation.isLoggedIn) {
      throw new Error(`Login validation failed for ${platform}`);
    }

    logger.success(`${platform} authentication successful!`);
    return true;
  } catch (error) {
    logger.error(`${platform} authentication failed:`, error);
    return false;
  }
}

// Helper function to verify authentication transfer
async function verifyAuthTransfer(page, platform, logger) {
  logger.info(`Verifying ${platform} authentication transfer`);

  const config = STEEL_PLATFORMS[platform];

  try {
    // Navigate to platform home page
    await page.goto(config.homeUrl);

    // Validate session
    const validation = await validateSession(page, platform, logger);

    if (validation.isLoggedIn) {
      logger.success(`${platform} authentication transfer successful!`);

      // Test navigation to demo URLs
      for (const demo of config.demoUrls.slice(0, 2)) {
        // Test first 2 URLs
        try {
          logger.info(`Testing ${demo.name}: ${demo.url}`);
          await page.goto(demo.url, { timeout: 30000 });
          await page.waitForTimeout(2000);

          const demoValidation = await validateSession(page, platform, logger);
          if (demoValidation.isLoggedIn) {
            logger.success(`✅ ${demo.name}: Session persisted`);
          } else {
            logger.warning(`⚠️ ${demo.name}: Session validation uncertain`);
          }
        } catch (error) {
          logger.warning(
            `❌ ${demo.name}: Navigation failed - ${error.message}`
          );
        }
      }

      return true;
    } else {
      logger.warning(`${platform} authentication transfer failed`);
      return false;
    }
  } catch (error) {
    logger.error(`Error verifying ${platform} authentication:`, error);
    return false;
  }
}

async function demonstratePlatform(platform) {
  const logger = new SessionLogger(platform, true);
  let session;
  let browser;

  try {
    logger.success(`=== Steel SDK Demo: ${platform.toUpperCase()} ===`);

    // Step 1: Create and authenticate initial session
    logger.info("Creating initial Steel session...");
    session = await client.sessions.create();
    logger.success(
      `Steel Session #1 created!\n` +
        `View session at: ${session.sessionViewerUrl}`
    );

    // Connect Playwright to the session
    browser = await chromium.connectOverCDP(
      `wss://connect.steel.dev?apiKey=${process.env.STEEL_API_KEY}&sessionId=${session.id}`
    );

    const page = await browser.contexts()[0].pages()[0];

    // Perform platform-specific login
    const loginSuccess = await performLogin(page, platform, logger);

    if (!loginSuccess) {
      throw new Error(`Failed to authenticate with ${platform}`);
    }

    // Step 2: Capture and transfer authentication
    logger.info("Capturing session context...");
    const sessionContext = await client.sessions.context(session.id);
    logger.info(
      `Captured session context with ${
        Object.keys(sessionContext).length
      } properties`
    );

    // Clean up first session
    await browser.close();
    await client.sessions.release(session.id);
    logger.info("Session #1 released");

    // Step 3: Create new authenticated session
    logger.info("Creating new session with transferred context...");
    session = await client.sessions.create({ sessionContext });
    logger.success(
      `Steel Session #2 created!\n` +
        `View session at: ${session.sessionViewerUrl}`
    );

    // Connect to new session
    browser = await chromium.connectOverCDP(
      `wss://connect.steel.dev?apiKey=${process.env.STEEL_API_KEY}&sessionId=${session.id}`
    );

    // Verify authentication transfer
    const newPage = await browser.contexts()[0].pages()[0];
    const transferSuccess = await verifyAuthTransfer(newPage, platform, logger);

    if (transferSuccess) {
      logger.success(
        `🎉 ${platform.toUpperCase()} session transfer completed successfully!`
      );
    } else {
      logger.warning(
        `⚠️ ${platform.toUpperCase()} session transfer had issues`
      );
    }

    return transferSuccess;
  } catch (error) {
    logger.error(`${platform} demo failed:`, error);
    return false;
  } finally {
    // Cleanup
    if (browser) {
      try {
        await browser.close();
      } catch (e) {
        logger.warning("Browser cleanup warning:", e.message);
      }
    }
    if (session) {
      try {
        await client.sessions.release(session.id);
        logger.info("Final session cleanup completed");
      } catch (e) {
        logger.warning("Session cleanup warning:", e.message);
      }
    }
  }
}

// Main execution function
async function main() {
  console.log("🚀 Steel SDK Session Persistence Demo");
  console.log("=====================================");

  const args = process.argv.slice(2);
  const platform = args[0] || "github";

  if (args.includes("--help") || args.includes("-h")) {
    console.log("\nUsage: node steel-session-demo.js [platform]");
    console.log("\nAvailable platforms:");
    console.log("  - github (default)");
    console.log("  - google");
    console.log("\nExamples:");
    console.log("  node steel-session-demo.js github");
    console.log("  node steel-session-demo.js google");
    console.log("\nThis demo will:");
    console.log("  1. Create a Steel session");
    console.log("  2. Guide you through manual authentication");
    console.log("  3. Capture the session context");
    console.log("  4. Create a new session with transferred authentication");
    console.log("  5. Verify the session transfer worked");
    return;
  }

  if (!["github", "google"].includes(platform)) {
    console.error(`❌ Unsupported platform: ${platform}`);
    console.error("Supported platforms: github, google");
    process.exit(1);
  }

  if (!process.env.STEEL_API_KEY) {
    console.error("❌ STEEL_API_KEY environment variable is required");
    console.error("Please set it in your .env file or environment");
    process.exit(1);
  }

  console.log(`\n🎯 Testing platform: ${platform.toUpperCase()}`);
  console.log(
    `🔑 Using Steel API Key: ${process.env.STEEL_API_KEY.substring(0, 20)}...`
  );

  try {
    const success = await demonstratePlatform(platform);

    if (success) {
      console.log(
        `\n🎉 Steel SDK demo completed successfully for ${platform.toUpperCase()}!`
      );
      console.log("\n✅ Key achievements:");
      console.log("  - Created Steel session");
      console.log("  - Performed manual authentication");
      console.log("  - Captured session context");
      console.log("  - Successfully transferred authentication to new session");
      console.log("  - Verified session persistence across navigation");
    } else {
      console.log(
        `\n⚠️ Steel SDK demo completed with issues for ${platform.toUpperCase()}`
      );
      console.log("\n💡 Troubleshooting tips:");
      console.log("  - Ensure you completed the authentication process");
      console.log("  - Check that the platform selectors are up to date");
      console.log("  - Verify your Steel API key is valid");
      console.log("  - Try running with a different platform");
    }
  } catch (error) {
    console.error(
      `\n❌ Steel SDK demo failed for ${platform.toUpperCase()}:`,
      error.message
    );
    console.error("\n🔍 Debug information:");
    console.error("  - Check your internet connection");
    console.error("  - Verify Steel API key is correct");
    console.error("  - Ensure all dependencies are installed");
    console.error("  - Check Steel service status");
    process.exit(1);
  }
}

// Run the demo
main().catch((error) => {
  console.error("❌ Unexpected error:", error);
  process.exit(1);
});
