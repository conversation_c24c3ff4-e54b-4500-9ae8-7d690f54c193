import { EventEmitter } from "events";
import puppeteer from "puppeteer";
import { execSync } from "child_process";
import { platform } from "os";

/**
 * Variant 1: Uses Chrome's getDisplayMedia API with auto-select-desktop-capture-source
 * This variant launches Chrome with special flags to enable screen capture and
 * injects a script that calls navigator.mediaDevices.getDisplayMedia()
 */
// Singleton browser instance shared across all Variant1 instances
let sharedBrowser = null;
let browserInitPromise = null;

export class Variant1 extends EventEmitter {
  constructor(config = {}) {
    super();
    this.config = {
      headless: false, // Must be false for screen capture
      width: config.width || 1920,
      height: config.height || 1080,
      ...config,
    };

    this.browser = null;
    this.page = null;
    this.mediaStream = null;
    this.isStreaming = false;
  }

  /**
   * Get or create the shared browser instance
   */
  async getSharedBrowser() {
    if (sharedBrowser && sharedBrowser.isConnected()) {
      return sharedBrowser;
    }

    // If browser initialization is already in progress, wait for it
    if (browserInitPromise) {
      return await browserInitPromise;
    }

    // Start browser initialization
    browserInitPromise = this.initializeBrowser();
    sharedBrowser = await browserInitPromise;
    browserInitPromise = null;

    return sharedBrowser;
  }

  /**
   * Initialize the browser instance (only called once)
   */
  async initializeBrowser() {
    console.log("🚀 Initializing shared browser instance...");

    const screenName = this.getScreenName();
    console.log(`Screen name: ${screenName}`);

    const browser = await puppeteer.launch({
      headless: this.config.headless,
      args: [
        "--remote-allow-origins=*",
        "--remote-debugging-port=9222",
        `--auto-select-desktop-capture-source=${screenName}`,
        "--auto-accept-this-tab-capture",
      ],
      defaultViewport: {
        width: this.config.width,
        height: this.config.height,
      },
    });

    console.log("✅ Shared browser instance created");
    return browser;
  }

  /**
   * Get the screen/window name for auto-select-desktop-capture-source
   */
  getScreenName() {
    const os = platform();

    if (os === "darwin") {
      // macOS - get the main display
      return "Entire screen";
    } else if (os === "win32") {
      // Windows - get the primary monitor
      return "Screen 1";
    } else {
      // Linux - get the first screen
      return ":0.0";
    }
  }

  /**
   * Inject WebRTC streaming script into the browser
   */
  async setupScreenCapture() {
    console.log("Setting up WebRTC injection...");

    // Get the shared browser instance
    this.browser = await this.getSharedBrowser();

    // Create a new page for this streaming session
    this.page = await this.browser.newPage();

    // Navigate to a proper HTTPS page for getDisplayMedia to work
    await this.page.goto("https://example.com", {
      waitUntil: "domcontentloaded",
    });

    // Read the WebRTC injector script
    const { readFileSync } = await import("fs");
    const { fileURLToPath } = await import("url");
    const { dirname, join } = await import("path");

    const __filename = fileURLToPath(import.meta.url);
    const __dirname = dirname(__filename);
    const injectorScript = readFileSync(
      join(__dirname, "browser-webrtc-injector.js"),
      "utf8"
    );

    console.log("Injecting WebRTC streaming script...");

    // Inject the WebRTC streaming script
    await this.page.addScriptTag({
      content: injectorScript,
    });

    // Wait a moment for script to load
    await new Promise((resolve) => setTimeout(resolve, 1000));

    // Start the WebRTC streaming using the injected script
    const result = await this.page.evaluate(async () => {
      try {
        console.log("Starting WebRTC streaming via injected script...");

        // Check if the WebRTC streamer is available
        if (!window.webrtcStreamer) {
          throw new Error("WebRTC streamer not available");
        }

        // Auto-start the WebRTC streaming
        await window.webrtcStreamer.autoStart();

        // Get status to confirm it's working
        const status = window.webrtcStreamer.getStatus();
        console.log("WebRTC streamer status:", status);

        return {
          success: true,
          method: "webrtc-injection",
          status: status,
          clientId: status.clientId,
          streaming: status.streaming,
        };
      } catch (error) {
        console.error("Error starting WebRTC streaming:", error);
        return {
          success: false,
          error: error.message,
        };
      }
    });

    if (result.success) {
      console.log("Screen capture successful:", result);
      this.mediaStream = result;
      this.emit("stream", result);
      return result;
    } else {
      const error = new Error(`Screen capture failed: ${result.error}`);
      this.emit("error", error);
      throw error;
    }
  }

  /**
   * Start the streaming process
   */
  async startStreaming() {
    if (this.isStreaming) {
      console.log("Already streaming");
      return;
    }

    try {
      console.log("Starting Variant 1 streaming (getDisplayMedia)...");

      await this.setupScreenCapture();

      this.isStreaming = true;
      console.log("Variant 1 streaming started successfully");
    } catch (error) {
      console.error("Error starting Variant 1 streaming:", error);
      this.emit("error", error);
      throw error;
    }
  }

  /**
   * Stop streaming and cleanup
   */
  async stopStreaming() {
    if (!this.isStreaming) {
      return;
    }

    console.log("Stopping Variant 1 streaming...");

    try {
      // Stop the media stream
      if (this.page) {
        await this.page.evaluate(() => {
          if (window.capturedStream) {
            window.capturedStream.getTracks().forEach((track) => track.stop());
            window.capturedStream = null;
          }
        });
      }
    } catch (error) {
      console.error("Error stopping media stream:", error);
    }

    this.isStreaming = false;
    console.log("Variant 1 streaming stopped");
  }

  /**
   * Get the current stream information
   */
  getStreamInfo() {
    return this.mediaStream;
  }

  /**
   * Cleanup resources (but keep shared browser running)
   */
  async cleanup() {
    console.log("Cleaning up Variant 1...");

    await this.stopStreaming();

    if (this.page) {
      await this.page.close();
      this.page = null;
    }

    // Don't close the shared browser - it will be reused by other instances
    this.browser = null;

    console.log("Variant 1 cleanup complete (shared browser still running)");
  }

  /**
   * Static method to cleanup the shared browser (call when shutting down server)
   */
  static async cleanupSharedBrowser() {
    if (sharedBrowser && sharedBrowser.isConnected()) {
      console.log("🧹 Closing shared browser instance...");
      await sharedBrowser.close();
      sharedBrowser = null;
      browserInitPromise = null;
      console.log("✅ Shared browser closed");
    }
  }
}
