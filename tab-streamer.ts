// Tab Screen Streamer - Browser Injectable Script
// Captures and streams the current tab's screen content to the UI layout component

import type { TabScreenStreamerStatus } from "./types/tab-screen-streamer";

// Type definitions
interface Config {
  frameRate: number;
}

interface WebSocketMessage {
  type: string;
  source?: string;
  [key: string]: any;
}

interface OfferMessage extends WebSocketMessage {
  type: "offer";
  offer: RTCSessionDescriptionInit;
  source: string;
}

interface AnswerMessage extends WebSocketMessage {
  type: "answer";
  answer: RTCSessionDescriptionInit;
  source: string;
}

interface CandidateMessage extends WebSocketMessage {
  type: "candidate";
  candidate: RTCIceCandidateInit;
  source: string;
}

interface ReadyMessage extends WebSocketMessage {
  type: "ready";
  source: string;
}

interface InitTabStreamerMessage extends WebSocketMessage {
  type: "init-tab-streamer";
  wsEndpoint: string;
  source: string;
}

interface StartTabStreamingMessage extends WebSocketMessage {
  type: "start-tab-streaming";
  source: string;
}

interface StopTabStreamingMessage extends WebSocketMessage {
  type: "stop-tab-streaming";
  source: string;
}

interface StartStreamingMessage extends WebSocketMessage {
  type: "start-streaming";
}

interface StopStreamingMessage extends WebSocketMessage {
  type: "stop-streaming";
}

interface StreamingStatusMessage {
  type: "streaming-status";
  status: "started" | "stopped";
}

interface TabStreamingStatusMessage {
  type: "tab-streaming-status";
  status: string;
  timestamp: number;
  source: string;
  error?: string;
  reason?: string;
}

(function (): void {
  const config: Config = {
    frameRate: 15,
  };

  let socket: WebSocket | null = null;
  let pc: RTCPeerConnection | null = null;
  let screenStream: MediaStream | null = null;
  let isInitialized: boolean = false;
  let isStreaming: boolean = false;
  let inputChannel: RTCDataChannel | null = null;

  /**
   * Initialize tab screen streamer with WebSocket and WebRTC connections
   * This method sets up the connections but doesn't start streaming
   */
  async function init(
    wsEndpoint: string
  ): Promise<"SUCCESS" | "ALREADY_INITIALIZED"> {
    if (isInitialized) {
      return "ALREADY_INITIALIZED";
    }

    try {
      // Step 1: Setup WebSocket connection
      await connectToSocket(wsEndpoint);

      // Step 2: Setup WebRTC connection
      await connectToWebRTC();

      // Step 3: Setup message handlers
      setupSocketMessageHandlers();

      isInitialized = true;
      return "SUCCESS";
    } catch (err) {
      throw err;
    }
  }

  /**
   * Connect to WebSocket and wait for it to be ready
   */
  async function connectToSocket(wsEndpoint: string): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        if (!wsEndpoint) {
          throw new Error(
            `[kazeel][tab-screen-streamer] WebSocket endpoint is required`
          );
        }

        socket = new WebSocket(wsEndpoint);

        // Expose socket to window for external access
        window.tabStreamerSocket = socket;

        socket.addEventListener("open", () => {
          resolve();
        });

        socket.addEventListener("error", (e: Event) => {
          const errorEvent = e as ErrorEvent;
          reject(
            new Error(
              `[kazeel][tab-screen-streamer] WebSocket connection failed: ${
                errorEvent.message || "Unknown error"
              }`
            )
          );
        });

        socket.addEventListener("close", (e: CloseEvent) => {
          // WebSocket closed
        });
      } catch (err) {
        reject(err);
      }
    });
  }

  /**
   * Connect to WebRTC and set up peer connection
   */
  async function connectToWebRTC(): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        pc = new RTCPeerConnection({
          iceServers: [
            { urls: "stun:stun.cloudflare.com:3478" },
            {
              urls: "turn:relay1.expressturn.com:3478",
              username: "ef89RMU4SHUQMSOUU9",
              credential: "jvkMMnQxWX4Qrhe3",
            },
          ],
        });

        pc.onicecandidate = (event: RTCPeerConnectionIceEvent) => {
          if (event.candidate && socket?.readyState === WebSocket.OPEN) {
            socket.send(
              JSON.stringify({
                type: "candidate",
                candidate: event.candidate,
                source: "tab-stream",
              } as CandidateMessage)
            );
          }
        };

        pc.onconnectionstatechange = (): void => {
          // Connection state changed
        };

        pc.oniceconnectionstatechange = (): void => {
          // ICE connection state changed
        };

        resolve();
      } catch (err) {
        reject(err);
      }
    });
  }

  /**
   * Set up WebSocket message handlers
   */
  function setupSocketMessageHandlers(): void {
    if (!socket) return;

    socket.addEventListener("message", async (event: MessageEvent) => {
      let msg: WebSocketMessage;
      try {
        msg = JSON.parse(event.data);
      } catch (err) {
        return;
      }

      switch (msg.type) {
        case "offer":
          // Only handle offers from UI
          if (msg.source === "ui" && pc) {
            const offerMsg = msg as OfferMessage;
            await pc.setRemoteDescription(
              new RTCSessionDescription(offerMsg.offer)
            );
            const answer = await pc.createAnswer();
            await pc.setLocalDescription(answer);
            if (socket?.readyState === WebSocket.OPEN) {
              socket.send(
                JSON.stringify({
                  type: "answer",
                  answer,
                  source: "tab-stream",
                } as AnswerMessage)
              );
            }
          }
          break;
        case "answer":
          // Only handle answers from UI
          if (msg.source === "ui" && pc) {
            const answerMsg = msg as AnswerMessage;
            await pc.setRemoteDescription(
              new RTCSessionDescription(answerMsg.answer)
            );
          }
          break;
        case "candidate":
          // Only handle candidates from UI
          if (msg.source === "ui" && pc) {
            const candidateMsg = msg as CandidateMessage;
            await pc.addIceCandidate(
              new RTCIceCandidate(candidateMsg.candidate)
            );
          }
          break;
        case "ready":
          // UI is ready, create offer from tab stream side
          if (msg.source === "ui" && pc) {
            createInputChannel();

            const offer = await pc.createOffer();
            await pc.setLocalDescription(offer);
            if (socket?.readyState === WebSocket.OPEN) {
              socket.send(
                JSON.stringify({
                  type: "offer",
                  offer,
                  source: "tab-stream",
                } as OfferMessage)
              );
            }
          }
          break;
        case "init-tab-streamer":
          // Initialize tab screen streamer from UI request
          if (msg.source === "ui" && socket?.readyState === WebSocket.OPEN) {
            const initMsg = msg as InitTabStreamerMessage;
            try {
              const result = await init(initMsg.wsEndpoint);
              socket.send(
                JSON.stringify({
                  type: "tab-streamer-initialized",
                  result: result,
                  source: "tab-stream",
                })
              );
            } catch (error) {
              const errorObj = error as Error;
              socket.send(
                JSON.stringify({
                  type: "tab-streaming-status",
                  status: "error",
                  error: errorObj.message,
                  source: "tab-stream",
                } as TabStreamingStatusMessage)
              );
            }
          }
          break;
        case "start-tab-streaming":
          // Start tab streaming from UI request
          if (msg.source === "ui" && socket?.readyState === WebSocket.OPEN) {
            const startMsg = msg as StartTabStreamingMessage;
            try {
              const result = await start();
              socket.send(
                JSON.stringify({
                  type: "tab-streaming-started",
                  result: result,
                  source: "tab-stream",
                })
              );
            } catch (error) {
              const errorObj = error as Error;
              socket.send(
                JSON.stringify({
                  type: "tab-streaming-status",
                  status: "error",
                  error: errorObj.message,
                  source: "tab-stream",
                } as TabStreamingStatusMessage)
              );
            }
          }
          break;
        case "stop-tab-streaming":
          // Stop tab streaming from UI request
          if (msg.source === "ui" && socket?.readyState === WebSocket.OPEN) {
            try {
              const result = await stop();
              socket.send(
                JSON.stringify({
                  type: "tab-streaming-stopped",
                  result: result,
                  source: "tab-stream",
                })
              );
            } catch (error) {
              const errorObj = error as Error;
              socket.send(
                JSON.stringify({
                  type: "tab-streaming-status",
                  status: "error",
                  error: errorObj.message,
                  source: "tab-stream",
                } as TabStreamingStatusMessage)
              );
            }
          }
          break;
        case "start-streaming":
          await startStreaming();
          break;
        case "stop-streaming":
          await stopStreaming();
          break;
        default:
        // Unknown message type
      }
    });
  }

  /**
   * Create input data channel for communication
   */
  function createInputChannel(): void {
    if (!pc) return;

    try {
      inputChannel = pc.createDataChannel("input", {
        ordered: true,
      });

      inputChannel.onopen = (): void => {
        // Input channel opened
      };

      inputChannel.onclose = (): void => {
        // Input channel closed
      };

      inputChannel.onerror = (error: Event): void => {
        // Input channel error
      };

      inputChannel.onmessage = async (event: MessageEvent): Promise<void> => {
        try {
          const data = JSON.parse(event.data);

          // Handle input events or other messages from the UI
          if (data.type === "request-frame") {
            // Request a new frame if needed
          }
        } catch (err) {
          // Failed to handle input channel message
        }
      };
    } catch (err) {
      throw err;
    }
  }

  /**
   * Start streaming the current tab's screen content
   */
  async function startStreaming(): Promise<void> {
    if (isStreaming || !pc) {
      return;
    }
    try {
      // Get display media stream for current tab
      screenStream = await navigator.mediaDevices.getDisplayMedia({
        video: {
          frameRate: config.frameRate,
        },
      });

      // Add the video track to the peer connection
      const videoTrack = screenStream.getVideoTracks()[0];
      pc.addTrack(videoTrack, screenStream);

      isStreaming = true;

      // Notify the UI that streaming has started
      if (socket?.readyState === WebSocket.OPEN) {
        socket.send(
          JSON.stringify({
            type: "streaming-status",
            status: "started",
          } as StreamingStatusMessage)
        );
      }
    } catch (err) {
      throw err;
    }
  }

  /**
   * Stop streaming
   */
  async function stopStreaming(): Promise<void> {
    if (!isStreaming) {
      return;
    }

    try {
      if (screenStream) {
        screenStream.getTracks().forEach((track) => {
          track.stop();
        });
        screenStream = null;
      }

      isStreaming = false;

      // Notify the UI that streaming has stopped
      if (socket?.readyState === WebSocket.OPEN) {
        socket.send(
          JSON.stringify({
            type: "streaming-status",
            status: "stopped",
          } as StreamingStatusMessage)
        );
      }
    } catch (err) {
      throw err;
    }
  }

  /**
   * Send status update to the UI layout
   */
  function sendStatusUpdate(
    status: string,
    data: Record<string, any> = {}
  ): void {
    if (socket?.readyState === WebSocket.OPEN) {
      const message: TabStreamingStatusMessage = {
        type: "tab-streaming-status",
        status,
        timestamp: Date.now(),
        source: "tab-stream",
        ...data,
      };
      socket.send(JSON.stringify(message));
    }
  }

  /**
   * Handle stream track ended event
   */
  function handleStreamEnded(): void {
    isStreaming = false;
    sendStatusUpdate("ended", {
      reason: "track_ended",
    });
  }

  /**
   * Enhanced start streaming with error handling and status updates
   */
  async function start(): Promise<"SUCCESS"> {
    if (!isInitialized) {
      throw new Error(
        "[kazeel][tab-screen-streamer] Not initialized. Call init() first."
      );
    }

    try {
      await startStreaming();

      // Set up stream ended handler
      if (screenStream) {
        const videoTrack = screenStream.getVideoTracks()[0];
        videoTrack.addEventListener("ended", handleStreamEnded);
      }

      return "SUCCESS";
    } catch (err) {
      const errorObj = err as Error;
      sendStatusUpdate("error", {
        error: errorObj.message,
      });
      throw err;
    }
  }

  /**
   * Enhanced stop streaming with cleanup
   */
  async function stop(): Promise<"SUCCESS"> {
    try {
      await stopStreaming();
      return "SUCCESS";
    } catch (err) {
      const errorObj = err as Error;
      sendStatusUpdate("error", {
        error: errorObj.message,
      });
      throw err;
    }
  }

  /**
   * Get current streaming status
   */
  function getStatus(): TabScreenStreamerStatus {
    return {
      initialized: isInitialized,
      streaming: isStreaming,
      socketReady: socket?.readyState === WebSocket.OPEN,
      webrtcState: pc?.connectionState || "new",
      iceState: pc?.iceConnectionState || "new",
    };
  }

  /**
   * Cleanup all resources
   */
  function cleanup(): void {
    if (isStreaming) {
      stopStreaming();
    }

    if (pc) {
      pc.close();
      pc = null;
    }

    if (socket) {
      socket.close();
      socket = null;
    }

    if (window.tabStreamerSocket) {
      window.tabStreamerSocket = undefined;
    }

    isInitialized = false;
    isStreaming = false;
    inputChannel = null;
  }

  // Expose the tab screen streamer interface
  window.tabScreenStreamer = {
    init,
    start,
    stop,
    startStreaming, // Legacy method
    stopStreaming, // Legacy method
    getStatus,
    cleanup,
  };

  // Cleanup on page unload
  window.addEventListener("beforeunload", cleanup);
})();
