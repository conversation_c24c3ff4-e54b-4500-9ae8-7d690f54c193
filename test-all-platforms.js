/**
 * Comprehensive Platform Testing Script
 * Tests session persistence across GitHub, Facebook, and Google
 */

import { spawn } from "child_process";
import fs from "fs";
import path from "path";
import { PLATFORMS } from "./session-utils.js";

const PLATFORMS_TO_TEST = ["github", "google", "facebook"];
const TEST_RESULTS = {};

function runCommand(command, args = []) {
  return new Promise((resolve, reject) => {
    console.log(`\n🚀 Running: ${command} ${args.join(" ")}`);

    const process = spawn(command, args, {
      stdio: "inherit",
      shell: true,
    });

    process.on("close", (code) => {
      if (code === 0) {
        resolve(code);
      } else {
        reject(new Error(`Command failed with exit code ${code}`));
      }
    });

    process.on("error", (error) => {
      reject(error);
    });
  });
}

async function testPlatform(platform) {
  console.log(`\n${"=".repeat(60)}`);
  console.log(`🧪 TESTING PLATFORM: ${platform.toUpperCase()}`);
  console.log(`${"=".repeat(60)}`);

  const sessionPath = path.join("sessions", platform);
  const testResultsFile = path.join(sessionPath, "test_results.json");

  try {
    // Check if session exists
    const cookiesFile = path.join(sessionPath, "cookies.json");
    const sessionExists = fs.existsSync(cookiesFile);

    console.log(`📁 Session exists: ${sessionExists ? "✅ Yes" : "❌ No"}`);

    if (!sessionExists) {
      console.log(`⚠️  No existing session found for ${platform}`);
      console.log(`💡 Please run: node enhanced-session-inject.js ${platform}`);
      console.log(`   to create a session first, then run this test again.`);

      TEST_RESULTS[platform] = {
        status: "skipped",
        reason: "No session found",
        recommendation: `Run: node enhanced-session-inject.js ${platform}`,
      };
      return;
    }

    // Test session injection with comprehensive tests
    console.log(`🔄 Testing session injection with comprehensive tests...`);
    await runCommand("node", [
      "enhanced-session-inject.js",
      platform,
      "--test",
    ]);

    // Read test results
    if (fs.existsSync(testResultsFile)) {
      const results = JSON.parse(fs.readFileSync(testResultsFile, "utf8"));
      const passed = results.filter((r) => r.success).length;
      const total = results.length;

      console.log(`\n📊 TEST RESULTS FOR ${platform.toUpperCase()}:`);
      console.log(`   ✅ Passed: ${passed}/${total}`);
      console.log(`   ❌ Failed: ${total - passed}/${total}`);

      results.forEach((result) => {
        const status = result.success ? "✅" : "❌";
        const error = result.error ? ` (${result.error})` : "";
        console.log(`   ${status} ${result.name}: ${result.url}${error}`);
      });

      TEST_RESULTS[platform] = {
        status: "tested",
        passed: passed,
        total: total,
        success_rate: ((passed / total) * 100).toFixed(1),
        details: results,
      };
    } else {
      console.log(`⚠️  Test results file not found for ${platform}`);
      TEST_RESULTS[platform] = {
        status: "completed",
        reason: "Test results file not found",
      };
    }
  } catch (error) {
    console.error(`❌ Error testing ${platform}:`, error.message);
    TEST_RESULTS[platform] = {
      status: "error",
      error: error.message,
    };
  }
}

async function generateReport() {
  console.log(`\n${"=".repeat(80)}`);
  console.log(`📋 COMPREHENSIVE TEST REPORT`);
  console.log(`${"=".repeat(80)}`);

  let totalPlatforms = 0;
  let testedPlatforms = 0;
  let successfulPlatforms = 0;

  for (const platform of PLATFORMS_TO_TEST) {
    totalPlatforms++;
    const result = TEST_RESULTS[platform];

    console.log(`\n🔍 ${platform.toUpperCase()}:`);

    if (result.status === "tested") {
      testedPlatforms++;
      const successRate = parseFloat(result.success_rate);
      if (successRate >= 80) {
        successfulPlatforms++;
        console.log(
          `   ✅ Status: EXCELLENT (${result.success_rate}% success rate)`
        );
      } else if (successRate >= 60) {
        console.log(
          `   ⚠️  Status: GOOD (${result.success_rate}% success rate)`
        );
      } else {
        console.log(
          `   ❌ Status: NEEDS IMPROVEMENT (${result.success_rate}% success rate)`
        );
      }
      console.log(`   📊 Tests: ${result.passed}/${result.total} passed`);
    } else if (result.status === "skipped") {
      console.log(`   ⏭️  Status: SKIPPED - ${result.reason}`);
      console.log(`   💡 Action: ${result.recommendation}`);
    } else if (result.status === "error") {
      console.log(`   ❌ Status: ERROR - ${result.error}`);
    } else {
      console.log(`   ✅ Status: COMPLETED (manual verification needed)`);
    }
  }

  console.log(`\n${"=".repeat(80)}`);
  console.log(`📈 SUMMARY:`);
  console.log(`   🎯 Platforms tested: ${testedPlatforms}/${totalPlatforms}`);
  console.log(
    `   ✅ Successful platforms: ${successfulPlatforms}/${testedPlatforms}`
  );
  console.log(
    `   📊 Overall success rate: ${
      testedPlatforms > 0
        ? ((successfulPlatforms / testedPlatforms) * 100).toFixed(1)
        : 0
    }%`
  );

  // Save detailed report
  const reportPath = "test-report.json";
  fs.writeFileSync(
    reportPath,
    JSON.stringify(
      {
        timestamp: new Date().toISOString(),
        summary: {
          total_platforms: totalPlatforms,
          tested_platforms: testedPlatforms,
          successful_platforms: successfulPlatforms,
          overall_success_rate:
            testedPlatforms > 0
              ? ((successfulPlatforms / testedPlatforms) * 100).toFixed(1)
              : 0,
        },
        results: TEST_RESULTS,
      },
      null,
      2
    )
  );

  console.log(`\n💾 Detailed report saved to: ${reportPath}`);
  console.log(`${"=".repeat(80)}`);
}

// Main execution
(async () => {
  console.log(`🧪 Starting comprehensive platform testing...`);
  console.log(`📋 Platforms to test: ${PLATFORMS_TO_TEST.join(", ")}`);

  for (const platform of PLATFORMS_TO_TEST) {
    await testPlatform(platform);
  }

  await generateReport();

  console.log(`\n🎉 Testing completed!`);
  console.log(`\n💡 Next steps:`);
  console.log(`   1. Review the test report above`);
  console.log(
    `   2. For any failed platforms, check the specific error messages`
  );
  console.log(`   3. For skipped platforms, create sessions first`);
  console.log(`   4. Re-run tests after making improvements`);
})().catch((error) => {
  console.error("❌ Test script failed:", error);
  process.exit(1);
});
