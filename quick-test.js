#!/usr/bin/env node

/**
 * Quick test to verify the streaming system fixes
 */

import WebSocket from 'ws';

async function testConnection() {
    console.log('🧪 Testing connection to streaming server...');
    
    return new Promise((resolve, reject) => {
        const ws = new WebSocket('ws://localhost:8080');
        let connected = false;
        
        ws.on('open', () => {
            console.log('✅ WebSocket connected');
            connected = true;
            
            // Test stream request
            console.log('📡 Requesting stream...');
            ws.send(JSON.stringify({ type: 'request-stream' }));
        });

        ws.on('message', (data) => {
            const message = JSON.parse(data.toString());
            console.log(`📨 Received: ${message.type}`);
            
            if (message.type === 'connected') {
                console.log(`✅ Connected with Variant ${message.variant}`);
            }
            
            if (message.type === 'webrtc-not-implemented') {
                console.log(`ℹ️  WebRTC: ${message.message}`);
            }
            
            if (message.type === 'stream-starting') {
                console.log('✅ Stream starting confirmed');
            }
            
            if (message.type === 'stream-ready') {
                console.log('✅ Stream ready!');
                setTimeout(() => {
                    ws.close();
                    resolve('success');
                }, 1000);
            }
            
            if (message.type === 'error') {
                console.log(`❌ Error: ${message.message}`);
                if (message.variant === 1) {
                    console.log('ℹ️  This is expected for Variant 1 - try Variant 2');
                }
                setTimeout(() => {
                    ws.close();
                    resolve('error-expected');
                }, 1000);
            }
        });

        ws.on('error', (error) => {
            console.error('❌ WebSocket error:', error.message);
            reject(error);
        });

        ws.on('close', () => {
            if (connected) {
                console.log('🔌 Connection closed');
            } else {
                reject(new Error('Connection failed'));
            }
        });

        // Timeout after 15 seconds
        setTimeout(() => {
            if (!connected) {
                ws.close();
                reject(new Error('Connection timeout'));
            }
        }, 15000);
    });
}

// Run test
testConnection()
    .then((result) => {
        console.log(`\n🎉 Test completed: ${result}`);
        process.exit(0);
    })
    .catch((error) => {
        console.error(`\n💥 Test failed: ${error.message}`);
        process.exit(1);
    });
