<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Remote Browser Streaming Client</title>
    <style>
      body {
        font-family: Arial, sans-serif;
        margin: 0;
        padding: 20px;
        background-color: #f0f0f0;
      }

      .container {
        max-width: 1200px;
        margin: 0 auto;
        background: white;
        border-radius: 8px;
        padding: 20px;
        box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      }

      .header {
        text-align: center;
        margin-bottom: 30px;
      }

      .controls {
        display: flex;
        gap: 10px;
        margin-bottom: 20px;
        flex-wrap: wrap;
        align-items: center;
      }

      .status {
        padding: 10px;
        border-radius: 4px;
        margin-bottom: 20px;
        font-weight: bold;
      }

      .status.disconnected {
        background-color: #ffebee;
        color: #c62828;
      }
      .status.connecting {
        background-color: #fff3e0;
        color: #ef6c00;
      }
      .status.connected {
        background-color: #e8f5e8;
        color: #2e7d32;
      }
      .status.streaming {
        background-color: #e3f2fd;
        color: #1565c0;
      }
      .status.error {
        background-color: #ffebee;
        color: #c62828;
      }

      .video-container {
        position: relative;
        background: #000;
        border-radius: 8px;
        overflow: hidden;
        min-height: 400px;
        display: flex;
        align-items: center;
        justify-content: center;
      }

      #remoteVideo {
        width: 100%;
        height: auto;
        max-height: 80vh;
      }

      .placeholder {
        color: #666;
        font-size: 18px;
        text-align: center;
      }

      button {
        padding: 10px 20px;
        border: none;
        border-radius: 4px;
        background-color: #1976d2;
        color: white;
        cursor: pointer;
        font-size: 14px;
      }

      button:hover {
        background-color: #1565c0;
      }

      button:disabled {
        background-color: #ccc;
        cursor: not-allowed;
      }

      .info {
        margin-top: 20px;
        padding: 15px;
        background-color: #f5f5f5;
        border-radius: 4px;
        font-family: monospace;
        font-size: 12px;
      }

      .logs {
        max-height: 200px;
        overflow-y: auto;
        background: #f8f8f8;
        padding: 10px;
        border-radius: 4px;
        margin-top: 10px;
        font-family: monospace;
        font-size: 12px;
      }

      .log-entry {
        margin-bottom: 5px;
        padding: 2px 0;
      }

      .log-entry.error {
        color: #d32f2f;
      }
      .log-entry.info {
        color: #1976d2;
      }
      .log-entry.success {
        color: #388e3c;
      }
    </style>
  </head>
  <body>
    <div class="container">
      <div class="header">
        <h1>Remote Browser Streaming Client</h1>
        <p>Connect to the streaming server to view the remote browser</p>
      </div>

      <div class="controls">
        <button id="connectBtn">Connect to Server</button>
        <button id="requestStreamBtn" disabled>Request Stream</button>
        <button id="disconnectBtn" disabled>Disconnect</button>
        <span
          >Server:
          <input
            type="text"
            id="serverUrl"
            value="ws://localhost:8080"
            style="padding: 8px; margin-left: 5px"
        /></span>
      </div>

      <div id="status" class="status disconnected">Disconnected</div>

      <div class="video-container">
        <video
          id="remoteVideo"
          autoplay
          playsinline
          muted
          style="display: none"
        ></video>
        <div id="placeholder" class="placeholder">
          Click "Connect to Server" and then "Request Stream" to start viewing
          the remote browser
        </div>
      </div>

      <div class="info">
        <div><strong>Connection Info:</strong></div>
        <div>Client ID: <span id="clientId">-</span></div>
        <div>Variant: <span id="variant">-</span></div>
        <div>WebRTC State: <span id="webrtcState">-</span></div>
        <div>Stream State: <span id="streamState">-</span></div>
      </div>

      <div class="logs" id="logs">
        <div class="log-entry info">Client ready. Click Connect to start.</div>
      </div>
    </div>

    <script>
      class StreamingClient {
        constructor() {
          this.ws = null;
          this.peerConnection = null;
          this.clientId = null;
          this.variant = null;
          this.isConnected = false;

          this.setupUI();
          this.log("Client initialized", "info");
        }

        setupUI() {
          this.connectBtn = document.getElementById("connectBtn");
          this.requestStreamBtn = document.getElementById("requestStreamBtn");
          this.disconnectBtn = document.getElementById("disconnectBtn");
          this.serverUrlInput = document.getElementById("serverUrl");
          this.statusDiv = document.getElementById("status");
          this.video = document.getElementById("remoteVideo");
          this.placeholder = document.getElementById("placeholder");

          this.connectBtn.addEventListener("click", () => this.connect());
          this.requestStreamBtn.addEventListener("click", () =>
            this.requestStream()
          );
          this.disconnectBtn.addEventListener("click", () => this.disconnect());
        }

        async connect() {
          const serverUrl = this.serverUrlInput.value.trim();
          if (!serverUrl) {
            this.log("Please enter a server URL", "error");
            return;
          }

          try {
            this.updateStatus("connecting", "Connecting to server...");
            this.log(`Connecting to ${serverUrl}`, "info");

            this.ws = new WebSocket(serverUrl);

            this.ws.onopen = () => {
              this.isConnected = true;
              this.updateStatus("connected", "Connected to server");
              this.log("WebSocket connected", "success");
              this.updateUI();
            };

            this.ws.onmessage = (event) => {
              this.handleServerMessage(JSON.parse(event.data));
            };

            this.ws.onclose = () => {
              this.isConnected = false;
              this.updateStatus("disconnected", "Disconnected from server");
              this.log("WebSocket disconnected", "info");
              this.updateUI();
            };

            this.ws.onerror = (error) => {
              this.log(`WebSocket error: ${error}`, "error");
              this.updateStatus("error", "Connection error");
            };
          } catch (error) {
            this.log(`Connection failed: ${error.message}`, "error");
            this.updateStatus("error", "Connection failed");
          }
        }

        disconnect() {
          if (this.ws) {
            this.ws.close();
          }
          if (this.peerConnection) {
            this.peerConnection.close();
            this.peerConnection = null;
          }
          this.isConnected = false;
          this.updateUI();
        }

        async requestStream() {
          if (!this.isConnected) {
            this.log("Not connected to server", "error");
            return;
          }

          this.log("Requesting stream from server", "info");
          this.sendToServer({ type: "request-stream" });

          // Setup WebRTC peer connection
          await this.setupWebRTC();
        }

        async setupWebRTC() {
          try {
            this.log("Setting up WebRTC peer connection", "info");

            this.peerConnection = new RTCPeerConnection({
              iceServers: [{ urls: "stun:stun.l.google.com:19302" }],
            });

            this.peerConnection.onicecandidate = (event) => {
              if (event.candidate) {
                this.sendToServer({
                  type: "webrtc-ice-candidate",
                  candidate: event.candidate,
                });
              }
            };

            this.peerConnection.ontrack = (event) => {
              this.log("Received remote stream", "success");
              this.video.srcObject = event.streams[0];
              this.video.style.display = "block";
              this.placeholder.style.display = "none";
              this.updateStatus("streaming", "Streaming active");
              document.getElementById("streamState").textContent = "Active";
            };

            this.peerConnection.onconnectionstatechange = () => {
              const state = this.peerConnection.connectionState;
              this.log(`WebRTC connection state: ${state}`, "info");
              document.getElementById("webrtcState").textContent = state;
            };

            // Create offer
            const offer = await this.peerConnection.createOffer();
            await this.peerConnection.setLocalDescription(offer);

            this.sendToServer({
              type: "webrtc-offer",
              offer: offer,
            });
          } catch (error) {
            this.log(`WebRTC setup failed: ${error.message}`, "error");
          }
        }

        handleServerMessage(message) {
          this.log(`Server message: ${message.type}`, "info");

          switch (message.type) {
            case "connected":
              this.clientId = message.clientId;
              this.variant = message.variant;
              document.getElementById("clientId").textContent = this.clientId;
              document.getElementById("variant").textContent = this.variant;
              break;

            case "stream-starting":
              this.log("Server is starting stream...", "info");
              break;

            case "stream-ready":
              this.log("Stream is ready on server", "success");
              break;

            case "webrtc-answer":
              this.handleWebRTCAnswer(message.answer);

              break;

            case "webrtc-ice-candidate":
              this.handleICECandidate(message.candidate);
              break;

            case "frame-data":
              this.handleFrameData(message.frame);
              break;

            case "error":
              this.log(`Server error: ${message.message}`, "error");
              break;

            case "pong":
              this.log("Pong received", "info");
              break;
          }
        }

        async handleWebRTCAnswer(answer) {
          try {
            await this.peerConnection.setRemoteDescription(answer);
            this.log("WebRTC answer processed", "success");
          } catch (error) {
            this.log(
              `Error processing WebRTC answer: ${error.message}`,
              "error"
            );
          }
        }

        async handleICECandidate(candidate) {
          try {
            // Check if remote description is set before adding ICE candidate
            if (this.peerConnection.remoteDescription) {
              await this.peerConnection.addIceCandidate(candidate);
              this.log("ICE candidate added", "info");
            } else {
              this.log(
                "ICE candidate received but no remote description yet - ignoring",
                "info"
              );
            }
          } catch (error) {
            this.log(`Error adding ICE candidate: ${error.message}`, "error");
          }
        }

        handleFrameData(frame) {
          try {
            // Create a canvas element to display the frames
            if (!this.frameCanvas) {
              this.frameCanvas = document.createElement("canvas");
              this.frameCanvas.style.width = "100%";
              this.frameCanvas.style.height = "auto";
              this.frameCanvas.style.maxHeight = "80vh";

              // Replace the video element with canvas for frame display
              this.video.style.display = "none";
              this.placeholder.style.display = "none";
              this.video.parentNode.appendChild(this.frameCanvas);

              this.frameCtx = this.frameCanvas.getContext("2d");
              this.frameCount = 0;
            }

            // Create image from base64 data
            const img = new Image();
            img.onload = () => {
              // Set canvas size to match image
              this.frameCanvas.width = img.width;
              this.frameCanvas.height = img.height;

              // Draw the frame
              this.frameCtx.drawImage(img, 0, 0);

              this.frameCount++;

              // Update status
              this.updateStatus(
                "streaming",
                `Streaming - Frame ${frame.frameNumber}`
              );
              document.getElementById(
                "streamState"
              ).textContent = `Active - ${this.frameCount} frames`;

              // Log every 30 frames to avoid spam
              if (this.frameCount % 30 === 0) {
                this.log(`Received ${this.frameCount} frames`, "info");
              }
            };

            img.onerror = (error) => {
              this.log(`Error loading frame: ${error}`, "error");
            };

            // Set image source to base64 data
            img.src = `data:image/jpeg;base64,${frame.data}`;
          } catch (error) {
            this.log(`Error handling frame data: ${error.message}`, "error");
          }
        }

        sendToServer(message) {
          if (this.ws && this.ws.readyState === WebSocket.OPEN) {
            this.ws.send(JSON.stringify(message));
          }
        }

        updateStatus(type, message) {
          this.statusDiv.className = `status ${type}`;
          this.statusDiv.textContent = message;
        }

        updateUI() {
          this.connectBtn.disabled = this.isConnected;
          this.requestStreamBtn.disabled = !this.isConnected;
          this.disconnectBtn.disabled = !this.isConnected;
          this.serverUrlInput.disabled = this.isConnected;
        }

        log(message, type = "info") {
          const timestamp = new Date().toLocaleTimeString();
          const logEntry = document.createElement("div");
          logEntry.className = `log-entry ${type}`;
          logEntry.textContent = `[${timestamp}] ${message}`;

          const logsContainer = document.getElementById("logs");
          logsContainer.appendChild(logEntry);
          logsContainer.scrollTop = logsContainer.scrollHeight;

          console.log(`[${type.toUpperCase()}] ${message}`);
        }
      }

      // Initialize client when page loads
      document.addEventListener("DOMContentLoaded", () => {
        new StreamingClient();
      });
    </script>
  </body>
</html>
