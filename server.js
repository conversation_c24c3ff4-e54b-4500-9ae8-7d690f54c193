import WebSocket, { WebSocketServer } from "ws";
import { createServer } from "http";
import { readFileSync } from "fs";
import { fileURLToPath } from "url";
import { dirname, join } from "path";

// Import variant implementations
import { Variant1 } from "./variant1.js";
import { Variant2 } from "./variant2.js";

// Import configuration
import {
  config,
  getVariantConfig,
  validateConfig,
  printConfig,
} from "./config.js";

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

class StreamingServer {
  constructor(serverConfig = {}) {
    // Merge with global config
    this.config = {
      ...config.server,
      variant: config.variant,
      ...serverConfig,
    };
    this.clients = new Map();
    this.variant = null;
    this.flag = true;
    // Create HTTP server for serving client files
    this.httpServer = createServer((req, res) => {
      if (req.url === "/" || req.url === "/client.html") {
        res.writeHead(200, { "Content-Type": "text/html" });
        try {
          const clientHtml = readFileSync(
            join(__dirname, "client.html"),
            "utf8"
          );
          res.end(clientHtml);
        } catch (err) {
          res.end(
            "<h1>Client file not found</h1><p>Please create client.html</p>"
          );
        }
      } else {
        res.writeHead(404);
        res.end("Not found");
      }
    });

    // Create WebSocket server
    this.wss = new WebSocketServer({ server: this.httpServer });

    this.setupWebSocketHandlers();
    this.initializeVariant();
  }

  initializeVariant() {
    console.log(`Initializing Variant ${this.config.variant}`);

    // Get variant-specific configuration
    const variantConfig = getVariantConfig(this.config.variant);

    if (this.config.variant === 1) {
      this.variant = new Variant1(variantConfig);
    } else if (this.config.variant === 2) {
      this.variant = new Variant2(variantConfig);
    } else {
      throw new Error(`Invalid variant: ${this.config.variant}. Use 1 or 2.`);
    }

    // Set up variant event handlers
    this.variant.on("stream", (stream) => {
      console.log("Stream available from variant");
      this.broadcastToClients({ type: "stream-ready" });
    });

    this.variant.on("frame", (frameData) => {
      // Handle frame data from Variant 2 (CDP screencast)
      this.handleNewFrame(frameData);
    });

    this.variant.on("error", (error) => {
      console.error("Variant error:", error);
      this.broadcastToClients({ type: "error", message: error.message });
    });
  }

  setupWebSocketHandlers() {
    this.wss.on("connection", (ws, req) => {
      const clientId = this.generateClientId();
      console.log(
        `Client ${clientId} connected from ${req.socket.remoteAddress}`
      );

      this.clients.set(clientId, {
        ws,
        peerConnection: null,
        connected: true,
      });

      ws.on("message", async (data) => {
        try {
          const message = JSON.parse(data.toString());
          await this.handleClientMessage(clientId, message);
        } catch (error) {
          console.error(
            `Error handling message from client ${clientId}:`,
            error
          );
          this.sendToClient(clientId, {
            type: "error",
            message: "Invalid message format",
          });
        }
      });

      ws.on("close", () => {
        console.log(`Client ${clientId} disconnected`);
        this.clients.delete(clientId);
      });

      ws.on("error", (error) => {
        console.error(`WebSocket error for client ${clientId}:`, error);
        this.clients.delete(clientId);
      });

      // Send initial connection confirmation
      this.sendToClient(clientId, {
        type: "connected",
        clientId,
        variant: this.config.variant,
      });
    });
  }
  async handleClientMessage(clientId, message) {
    const client = this.clients.get(clientId);
    if (!client) return;

    console.log(`Message from client ${clientId}:`, message.type);

    switch (message.type) {
      case "request-stream":
        if (this.flag) {
          await this.handleStreamRequest(clientId);
          this.flag = false;
        }
        break;

      case "webrtc-offer":
        await this.handleWebRTCOffer(clientId, message.offer);
        break;

      case "webrtc-answer":
        await this.handleWebRTCAnswer(
          clientId,
          message.answer,
          message.targetClientId
        );
        break;

      case "webrtc-ice-candidate":
        await this.handleICECandidate(
          clientId,
          message.candidate,
          message.targetClientId
        );
        break;

      case "ping":
        this.sendToClient(clientId, { type: "pong" });
        break;

      default:
        console.warn(`Unknown message type: ${message.type}`);
    }
  }

  async handleStreamRequest(clientId) {
    try {
      console.log(`Starting stream for client ${clientId}`);
      await this.variant.startStreaming();
      this.sendToClient(clientId, { type: "stream-starting" });
    } catch (error) {
      console.error("Error starting stream:", error);

      let errorMessage = "Failed to start stream";

      // Provide helpful error messages based on variant and error type
      if (
        this.config.variant === 1 &&
        error.message.includes("getDisplayMedia")
      ) {
        errorMessage = `Variant 1 (getDisplayMedia) failed: ${error.message}\n\nTry using Variant 2 instead: VARIANT=2 node server.js`;
      } else {
        errorMessage = error.message;
      }

      this.sendToClient(clientId, {
        type: "error",
        message: errorMessage,
        variant: this.config.variant,
      });
    }
  }

  async handleWebRTCOffer(clientId, offer) {
    try {
      console.log(`Relaying WebRTC offer from client ${clientId} to browser`);

      // Just relay the offer to other clients (browser-to-browser WebRTC)
      this.broadcastToOthers(clientId, {
        type: "webrtc-offer",
        offer: offer,
        sourceClientId: clientId,
      });
    } catch (error) {
      console.error("Error relaying WebRTC offer:", error);
      this.sendToClient(clientId, {
        type: "error",
        message: "WebRTC relay failed",
      });
    }
  }

  async handleWebRTCAnswer(clientId, answer, targetClientId) {
    try {
      console.log(
        `Relaying WebRTC answer from client ${clientId} to ${targetClientId}`
      );

      // Send answer to specific target client
      if (targetClientId) {
        this.sendToClient(targetClientId, {
          type: "webrtc-answer",
          answer: answer,
          sourceClientId: clientId,
        });
      } else {
        // Fallback: broadcast to others
        this.broadcastToOthers(clientId, {
          type: "webrtc-answer",
          answer: answer,
          sourceClientId: clientId,
        });
      }
    } catch (error) {
      console.error("Error relaying WebRTC answer:", error);
    }
  }

  async handleICECandidate(clientId, candidate, targetClientId) {
    try {
      console.log(
        `Relaying ICE candidate from client ${clientId} to ${
          targetClientId || "all"
        }`
      );

      if (targetClientId) {
        // Send to specific target client
        this.sendToClient(targetClientId, {
          type: "webrtc-ice-candidate",
          candidate: candidate,
          sourceClientId: clientId,
        });
      } else {
        // Fallback: broadcast to other clients
        this.broadcastToOthers(clientId, {
          type: "webrtc-ice-candidate",
          candidate: candidate,
          sourceClientId: clientId,
        });
      }
    } catch (error) {
      console.error("Error relaying ICE candidate:", error);
    }
  }

  /**
   * Handle new frame data from variants (especially Variant 2)
   */
  handleNewFrame(frameData) {
    // For now, we'll send frame data directly to clients
    // In a full WebRTC implementation, this would be encoded into RTP packets
    this.broadcastToClients({
      type: "frame-data",
      frame: {
        data: frameData.data, // Base64 image data
        timestamp: frameData.timestamp,
        frameNumber: frameData.frameNumber,
        metadata: frameData.metadata,
      },
    });
  }

  sendToClient(clientId, message) {
    const client = this.clients.get(clientId);
    if (client && client.ws.readyState === WebSocket.OPEN) {
      client.ws.send(JSON.stringify(message));
    }
  }

  broadcastToClients(message) {
    this.clients.forEach((client, clientId) => {
      this.sendToClient(clientId, message);
    });
  }

  broadcastToOthers(excludeClientId, message) {
    this.clients.forEach((client, clientId) => {
      if (clientId !== excludeClientId) {
        this.sendToClient(clientId, message);
      }
    });
  }

  generateClientId() {
    return Math.random().toString(36).substring(2, 15);
  }

  async start() {
    return new Promise((resolve) => {
      this.httpServer.listen(this.config.port, () => {
        console.log(
          `Streaming server running on http://localhost:${this.config.port}`
        );
        console.log(`Using Variant ${this.config.variant}`);
        console.log(`WebSocket server ready for connections`);
        resolve();
      });
    });
  }

  async stop() {
    if (this.variant) {
      await this.variant.cleanup();
    }

    // Cleanup shared browser if using Variant 1
    if (this.config.variant === 1) {
      const { Variant1 } = await import("./variant1.js");
      await Variant1.cleanupSharedBrowser();
    }

    this.clients.forEach((client) => {
      client.ws.close();
    });

    this.wss.close();
    this.httpServer.close();
    console.log("Server stopped");
  }
}

// Validate configuration
try {
  validateConfig();
  printConfig();
} catch (error) {
  console.error("Configuration error:", error.message);
  process.exit(1);
}

// Start server
const server = new StreamingServer();

process.on("SIGINT", async () => {
  console.log("\nShutting down server...");
  await server.stop();
  process.exit(0);
});

server.start().catch(console.error);

export { StreamingServer };
