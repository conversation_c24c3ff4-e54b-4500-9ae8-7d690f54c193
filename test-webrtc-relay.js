#!/usr/bin/env node

/**
 * Test WebRTC relay functionality
 */

import WebSocket from 'ws';

async function testWebRTCRelay() {
    console.log('🧪 Testing WebRTC relay functionality...');
    
    // Create two WebSocket connections (simulating browser and viewer)
    const browserWS = new WebSocket('ws://localhost:8080');
    const viewerWS = new WebSocket('ws://localhost:8080');
    
    let browserConnected = false;
    let viewerConnected = false;
    let browserClientId = null;
    let viewerClientId = null;
    
    return new Promise((resolve, reject) => {
        // Browser connection
        browserWS.on('open', () => {
            console.log('🌐 Browser WebSocket connected');
            browserConnected = true;
            checkBothConnected();
        });
        
        browserWS.on('message', (data) => {
            const message = JSON.parse(data.toString());
            console.log(`📨 Browser received: ${message.type}`);
            
            if (message.type === 'connected') {
                browserClientId = message.clientId;
                console.log(`✅ Browser client ID: ${browserClientId}`);
            }
            
            if (message.type === 'webrtc-offer') {
                console.log(`📥 Browser received WebRTC offer from ${message.sourceClientId}`);
                
                // Send back an answer
                const answer = {
                    type: 'answer',
                    sdp: 'v=0\r\no=- 123 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\nm=video 9 UDP/TLS/RTP/SAVPF 96\r\n'
                };
                
                browserWS.send(JSON.stringify({
                    type: 'webrtc-answer',
                    answer: answer,
                    targetClientId: message.sourceClientId
                }));
                
                console.log(`📤 Browser sent WebRTC answer to ${message.sourceClientId}`);
            }
        });
        
        // Viewer connection
        viewerWS.on('open', () => {
            console.log('👁️  Viewer WebSocket connected');
            viewerConnected = true;
            checkBothConnected();
        });
        
        viewerWS.on('message', (data) => {
            const message = JSON.parse(data.toString());
            console.log(`📨 Viewer received: ${message.type}`);
            
            if (message.type === 'connected') {
                viewerClientId = message.clientId;
                console.log(`✅ Viewer client ID: ${viewerClientId}`);
            }
            
            if (message.type === 'webrtc-answer') {
                console.log(`📥 Viewer received WebRTC answer from ${message.sourceClientId}`);
                console.log('🎉 WebRTC relay test PASSED!');
                
                // Close connections
                browserWS.close();
                viewerWS.close();
                resolve('SUCCESS');
            }
        });
        
        function checkBothConnected() {
            if (browserConnected && viewerConnected) {
                setTimeout(() => {
                    // Request stream from browser
                    console.log('📡 Requesting stream from browser...');
                    browserWS.send(JSON.stringify({ type: 'request-stream' }));
                    
                    setTimeout(() => {
                        // Send WebRTC offer from viewer
                        console.log('📤 Sending WebRTC offer from viewer...');
                        const offer = {
                            type: 'offer',
                            sdp: 'v=0\r\no=- 456 2 IN IP4 127.0.0.1\r\ns=-\r\nt=0 0\r\nm=video 9 UDP/TLS/RTP/SAVPF 96\r\n'
                        };
                        
                        viewerWS.send(JSON.stringify({
                            type: 'webrtc-offer',
                            offer: offer
                        }));
                    }, 2000);
                }, 1000);
            }
        }
        
        // Error handling
        browserWS.on('error', (error) => {
            console.error('❌ Browser WebSocket error:', error);
            reject(error);
        });
        
        viewerWS.on('error', (error) => {
            console.error('❌ Viewer WebSocket error:', error);
            reject(error);
        });
        
        // Timeout
        setTimeout(() => {
            browserWS.close();
            viewerWS.close();
            reject(new Error('Test timeout'));
        }, 15000);
    });
}

// Run test
testWebRTCRelay()
    .then((result) => {
        console.log(`\n🎉 Test completed: ${result}`);
        process.exit(0);
    })
    .catch((error) => {
        console.error(`\n💥 Test failed: ${error.message}`);
        process.exit(1);
    });
