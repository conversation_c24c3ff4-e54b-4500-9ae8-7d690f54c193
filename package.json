{"name": "enhanced-session-manager", "version": "1.0.0", "description": "Enhanced browser session persistence system with comprehensive testing for GitHub, Google, and Facebook platforms", "main": "enhanced-session-inject.js", "type": "module", "scripts": {"start": "node enhanced-session-inject.js", "test": "node test-all-platforms.js", "test:github": "node enhanced-session-inject.js github --test", "test:google": "node enhanced-session-inject.js google --test", "test:facebook": "node enhanced-session-inject.js facebook --test", "create:github": "node enhanced-session-inject.js github", "create:google": "node enhanced-session-inject.js google", "create:facebook": "node enhanced-session-inject.js facebook", "steel:demo": "node steel-session-demo.js", "help": "node enhanced-session-inject.js --help", "legacy:start": "node pup.js"}, "keywords": ["browser", "session", "persistence", "authentication", "puppeteer", "playwright", "steel-sdk", "github", "google", "facebook", "testing", "automation"], "author": "Enhanced Session Manager", "license": "MIT", "dependencies": {"@browserbasehq/sdk": "^2.6.0", "@hyperbrowser/sdk": "^0.46.0", "cors": "^2.8.5", "dotenv": "^16.3.0", "express": "^5.1.0", "http": "^0.0.1-security", "http-proxy": "^1.18.1", "playwright": "^1.55.0", "puppeteer": "24.10.0", "puppeteer-page-proxy": "^1.3.0", "puppeteer-proxy": "^1.0.3", "simple-cdp": "^1.8.6", "steel-sdk": "^0.10.1", "ws": "^8.18.2"}, "devDependencies": {"@types/node": "^20.0.0"}, "engines": {"node": ">=18.0.0"}, "repository": {"type": "git", "url": "."}, "files": ["session-utils.js", "enhanced-session-inject.js", "test-all-platforms.js", "steel-session-demo.js", "ENHANCED_SESSION_README.md", "sessions/"]}