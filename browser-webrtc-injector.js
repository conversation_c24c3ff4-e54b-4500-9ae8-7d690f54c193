/**
 * Browser WebRTC Injector Script
 * Injectable script that establishes WebRTC connection with the streaming server
 * Inspired by tab-streamer pattern
 */

(function () {
  "use strict";

  // Configuration
  const config = {
    frameRate: 30,
    wsEndpoint: "ws://localhost:8080",
    iceServers: [
      { urls: "stun:stun.l.google.com:19302" },
      { urls: "stun:stun.cloudflare.com:3478" },
    ],
  };

  // State variables
  let isInitialized = false;
  let isStreaming = false;
  let socket = null;
  let peerConnections = new Map(); // Map of viewerClientId -> peerConnection
  let screenStream = null;
  let clientId = null;

  /**
   * Initialize WebRTC streaming connection
   */
  async function init(wsEndpoint = config.wsEndpoint) {
    if (isInitialized) {
      console.log("[WebRTC-Injector] Already initialized");
      return "ALREADY_INITIALIZED";
    }

    try {
      console.log("[WebRTC-Injector] Initializing...");

      // Step 1: Setup WebSocket connection
      await connectToSocket(wsEndpoint);

      // Step 2: Setup message handlers
      setupSocketMessageHandlers();

      isInitialized = true;
      console.log("[WebRTC-Injector] ✅ Initialized successfully");
      return "SUCCESS";
    } catch (error) {
      console.error("[WebRTC-Injector] ❌ Initialization failed:", error);
      throw error;
    }
  }

  /**
   * Connect to WebSocket server
   */
  function connectToSocket(wsEndpoint) {
    return new Promise((resolve, reject) => {
      try {
        socket = new WebSocket(wsEndpoint);

        socket.onopen = () => {
          console.log("[WebRTC-Injector] 🔌 WebSocket connected");
          resolve();
        };

        socket.onerror = (error) => {
          console.error("[WebRTC-Injector] ❌ WebSocket error:", error);
          reject(new Error("WebSocket connection failed"));
        };

        socket.onclose = () => {
          console.log("[WebRTC-Injector] 🔌 WebSocket disconnected");
          isInitialized = false;
        };
      } catch (error) {
        reject(error);
      }
    });
  }

  /**
   * Create a new WebRTC peer connection for a viewer
   */
  function createPeerConnection(viewerClientId) {
    const peerConnection = new RTCPeerConnection({
      iceServers: config.iceServers,
    });

    // Handle ICE candidates
    peerConnection.onicecandidate = (event) => {
      if (event.candidate && socket?.readyState === WebSocket.OPEN) {
        socket.send(
          JSON.stringify({
            type: "webrtc-ice-candidate",
            candidate: event.candidate,
            targetClientId: viewerClientId,
          })
        );
      }
    };

    // Handle connection state changes
    peerConnection.onconnectionstatechange = () => {
      console.log(
        `[WebRTC-Injector] 🔗 Connection state for ${viewerClientId}:`,
        peerConnection.connectionState
      );

      // Clean up closed connections
      if (
        peerConnection.connectionState === "closed" ||
        peerConnection.connectionState === "failed"
      ) {
        peerConnections.delete(viewerClientId);
        console.log(
          `[WebRTC-Injector] 🧹 Cleaned up connection for ${viewerClientId}`
        );
      }
    };

    // Handle incoming streams (if any)
    peerConnection.ontrack = (event) => {
      console.log(
        `[WebRTC-Injector] 📺 Received remote track from ${viewerClientId}:`,
        event.track.kind
      );
    };

    // Add the screen stream if available
    if (screenStream) {
      screenStream.getTracks().forEach((track) => {
        peerConnection.addTrack(track, screenStream);
      });
      console.log(
        `[WebRTC-Injector] 📹 Added screen stream to connection for ${viewerClientId}`
      );
    }

    return peerConnection;
  }

  /**
   * Setup WebSocket message handlers
   */
  function setupSocketMessageHandlers() {
    socket.onmessage = async (event) => {
      try {
        const message = JSON.parse(event.data);
        console.log("[WebRTC-Injector] 📨 Received:", message.type);

        switch (message.type) {
          case "connected":
            clientId = message.clientId;
            console.log(
              "[WebRTC-Injector] ✅ Connected with client ID:",
              clientId
            );
            break;

          case "webrtc-offer":
            // Handle incoming offer from another client (viewer)
            if (message.sourceClientId !== clientId) {
              await handleWebRTCOffer(message.offer, message.sourceClientId);
            }
            break;

          case "webrtc-answer":
            await handleWebRTCAnswer(message.answer);
            break;

          case "webrtc-ice-candidate":
            // Only handle ICE candidates from other clients
            if (message.sourceClientId !== clientId) {
              await handleICECandidate(
                message.candidate,
                message.sourceClientId
              );
            }
            break;

          case "stream-starting":
            console.log("[WebRTC-Injector] 🎥 Server is starting stream");
            break;

          case "stream-ready":
            console.log("[WebRTC-Injector] ✅ Server stream is ready");
            break;

          case "frame-data":
            // Handle frame data if needed for debugging
            break;

          case "error":
            console.error(
              "[WebRTC-Injector] ❌ Server error:",
              message.message
            );
            break;
        }
      } catch (error) {
        console.error("[WebRTC-Injector] ❌ Message handling error:", error);
      }
    };
  }

  /**
   * Handle incoming WebRTC offer from viewer
   */
  async function handleWebRTCOffer(offer, sourceClientId) {
    try {
      console.log(
        "[WebRTC-Injector] 📥 Handling WebRTC offer from viewer:",
        sourceClientId
      );

      // Create a new peer connection for this viewer
      const peerConnection = createPeerConnection(sourceClientId);
      peerConnections.set(sourceClientId, peerConnection);

      await peerConnection.setRemoteDescription(offer);

      // Create answer
      const answer = await peerConnection.createAnswer();
      await peerConnection.setLocalDescription(answer);

      // Send answer back via WebSocket
      socket.send(
        JSON.stringify({
          type: "webrtc-answer",
          answer: answer,
          targetClientId: sourceClientId,
        })
      );

      console.log("[WebRTC-Injector] ✅ WebRTC answer sent to viewer");
    } catch (error) {
      console.error("[WebRTC-Injector] ❌ Error handling WebRTC offer:", error);
    }
  }

  /**
   * Handle WebRTC answer from server (not used in browser-side injection)
   */
  async function handleWebRTCAnswer(answer) {
    try {
      console.log(
        "[WebRTC-Injector] ℹ️ WebRTC answer received (browser doesn't need this)"
      );
    } catch (error) {
      console.error(
        "[WebRTC-Injector] ❌ Error processing WebRTC answer:",
        error
      );
    }
  }

  /**
   * Handle ICE candidate from server
   */
  async function handleICECandidate(candidate, sourceClientId) {
    try {
      const peerConnection = peerConnections.get(sourceClientId);
      if (peerConnection && peerConnection.remoteDescription) {
        await peerConnection.addIceCandidate(candidate);
        console.log(
          `[WebRTC-Injector] ✅ ICE candidate added for ${sourceClientId}`
        );
      } else {
        console.log(
          `[WebRTC-Injector] ⚠️ No peer connection or remote description for ${sourceClientId}`
        );
      }
    } catch (error) {
      console.error("[WebRTC-Injector] ❌ Error adding ICE candidate:", error);
    }
  }

  /**
   * Start screen sharing and WebRTC streaming
   */
  async function startStreaming() {
    if (!isInitialized) {
      throw new Error("Not initialized. Call init() first.");
    }

    if (isStreaming) {
      console.log("[WebRTC-Injector] Already streaming");
      return "ALREADY_STREAMING";
    }

    try {
      console.log("[WebRTC-Injector] 🎥 Starting screen capture...");
      console.log(
        "[WebRTC-Injector] 🔍 Checking mediaDevices availability:",
        !!navigator.mediaDevices
      );
      console.log(
        "[WebRTC-Injector] 🔍 Checking getDisplayMedia availability:",
        !!navigator.mediaDevices?.getDisplayMedia
      );

      // Get display media stream
      screenStream = await navigator.mediaDevices.getDisplayMedia({
        video: {
          frameRate: config.frameRate,
          width: { ideal: 1920 },
          height: { ideal: 1080 },
        },
        preferCurrentTab: true,
      });

      console.log(
        "[WebRTC-Injector] ✅ Screen capture successful, tracks:",
        screenStream.getTracks().length
      );

      // Handle stream ended
      const videoTrack = screenStream.getVideoTracks()[0];
      videoTrack.addEventListener("ended", () => {
        console.log("[WebRTC-Injector] 🔴 Screen sharing ended");
        stopStreaming();
      });

      // Request stream from server (this will trigger the browser to be ready for offers)
      socket.send(
        JSON.stringify({
          type: "request-stream",
        })
      );

      isStreaming = true;
      console.log("[WebRTC-Injector] ✅ Streaming started successfully");
      console.log(
        "[WebRTC-Injector] 📺 Ready to receive WebRTC offers from viewers"
      );
      return "SUCCESS";
    } catch (error) {
      console.error("[WebRTC-Injector] ❌ Failed to start streaming:", error);
      throw error;
    }
  }

  /**
   * Stop streaming
   */
  function stopStreaming() {
    if (screenStream) {
      screenStream.getTracks().forEach((track) => track.stop());
      screenStream = null;
    }

    isStreaming = false;
    console.log("[WebRTC-Injector] 🛑 Streaming stopped");
  }

  /**
   * Get current status
   */
  function getStatus() {
    return {
      initialized: isInitialized,
      streaming: isStreaming,
      connected: socket?.readyState === WebSocket.OPEN,
      clientId: clientId,
      connectionCount: peerConnections.size,
      connections: Array.from(peerConnections.keys()),
    };
  }

  /**
   * Cleanup all resources
   */
  function cleanup() {
    stopStreaming();

    // Close all peer connections
    peerConnections.forEach((peerConnection, viewerId) => {
      peerConnection.close();
      console.log(`[WebRTC-Injector] 🧹 Closed connection for ${viewerId}`);
    });
    peerConnections.clear();

    if (socket) {
      socket.close();
      socket = null;
    }

    isInitialized = false;
    clientId = null;
    console.log("[WebRTC-Injector] 🧹 Cleanup completed");
  }

  // Expose the WebRTC injector interface
  window.webrtcStreamer = {
    init,
    startStreaming,
    stopStreaming,
    getStatus,
    cleanup,
    config,
  };

  // Auto-initialize and start streaming
  window.webrtcStreamer.autoStart = async function () {
    try {
      await init();
      await startStreaming();
      console.log("[WebRTC-Injector] 🚀 Auto-start completed");
    } catch (error) {
      console.error("[WebRTC-Injector] ❌ Auto-start failed:", error);
    }
  };

  // Cleanup on page unload
  window.addEventListener("beforeunload", cleanup);

  console.log(
    "[WebRTC-Injector] 📦 Script loaded. Use window.webrtcStreamer to control streaming."
  );
  console.log(
    "[WebRTC-Injector] 🚀 Quick start: window.webrtcStreamer.autoStart()"
  );
})();
