/**
 * Session Management Utilities
 * Provides reusable functions for browser session persistence
 */

import fs from "fs";
import path from "path";

/**
 * Enhanced platform configurations with better validation selectors
 */
export const PLATFORMS = {
  github: {
    name: "GitHub",
    loginUrl: "https://github.com/login",
    homeUrl: "https://github.com/",
    // Multiple selectors for better validation
    loginSelectors: [
      'meta[name="user-login"]',
      "[data-login]",
      ".Header-link--profile",
      'summary[aria-label*="View profile"]',
    ],
    loggedOutSelectors: [
      ".js-sign-in-button",
      'a[href="/login"]',
      '.btn-primary[href="/join"]',
    ],
    demoUrls: [
      {
        url: "https://github.com/?tab=repositories",
        name: "Your Repositories",
      },
      { url: "https://github.com/explore", name: "Explore" },
      { url: "https://github.com/settings/profile", name: "Profile Settings" },
    ],
  },
  google: {
    name: "Google",
    loginUrl: "https://accounts.google.com/signin",
    homeUrl: "https://myaccount.google.com/",
    // Generic selectors that work for any Google account
    loginSelectors: [
      "[data-email]",
      '.gb_A[aria-label*="Google Account"]',
      '.gb_A[aria-label*="Google apps"]',
      '[aria-label*="Google Account"]',
      ".gb_A .gb_B",
    ],
    loggedOutSelectors: [
      'a[href*="accounts.google.com/signin"]',
      '.gb_A[href*="accounts.google.com"]',
      'input[type="email"]',
      "#identifierId",
    ],
    demoUrls: [
      { url: "https://mail.google.com/mail/u/0/#inbox", name: "Gmail" },
      { url: "https://calendar.google.com/", name: "Calendar" },
      { url: "https://drive.google.com/", name: "Drive" },
    ],
  },
  facebook: {
    name: "Facebook",
    loginUrl: "https://facebook.com/login",
    homeUrl: "https://facebook.com/",
    loginSelectors: [
      '[data-testid="blue_bar_profile_link"]',
      '[aria-label="Your profile"]',
      'div[role="navigation"] image[alt]',
      '[data-testid="nav_profile_photo"]',
    ],
    loggedOutSelectors: [
      'input[name="email"]',
      'input[name="pass"]',
      'button[name="login"]',
      'a[href*="/login"]',
    ],
    demoUrls: [
      { url: "https://www.facebook.com/bookmarks/", name: "Bookmarks" },
      { url: "https://www.facebook.com/marketplace/", name: "Marketplace" },
      { url: "https://www.facebook.com/me", name: "Profile" },
    ],
  },
};

/**
 * Logger utility for consistent logging across the application
 */
export class SessionLogger {
  constructor(platform, verbose = false) {
    this.platform = platform;
    this.verbose = verbose;
    this.startTime = Date.now();
  }

  log(message, data = null) {
    const timestamp = new Date().toISOString();
    const elapsed = Date.now() - this.startTime;
    console.log(`[${timestamp}] [${this.platform}] [+${elapsed}ms] ${message}`);
    if (data && this.verbose) {
      console.log(JSON.stringify(data, null, 2));
    }
  }

  error(message, error = null) {
    const timestamp = new Date().toISOString();
    console.error(`[${timestamp}] [${this.platform}] ERROR: ${message}`);
    if (error) {
      console.error(error);
    }
  }

  success(message) {
    this.log(`✅ ${message}`);
  }

  warning(message) {
    this.log(`⚠️ ${message}`);
  }

  info(message) {
    this.log(`ℹ️ ${message}`);
  }
}

/**
 * Enhanced session validation that checks multiple indicators
 */
export async function validateSession(page, platform, logger) {
  logger.info(`Validating session for ${platform}`);

  const config = PLATFORMS[platform];
  if (!config) {
    throw new Error(`Unknown platform: ${platform}`);
  }

  try {
    // Wait for page to load
    await page.waitForTimeout(3000); // Give page time to load
  } catch (e) {
    logger.warning("Page load timeout, continuing...");
  }

  // Check for logged-in indicators
  let loggedInCount = 0;
  for (const selector of config.loginSelectors) {
    try {
      const element = await page.evaluate((sel) => {
        return document.querySelector(sel) !== null;
      }, selector);
      if (element) {
        loggedInCount++;
        logger.log(`Found login indicator: ${selector}`);
      }
    } catch (e) {
      // Selector might not be valid, continue
    }
  }

  // Check for logged-out indicators
  let loggedOutCount = 0;
  for (const selector of config.loggedOutSelectors) {
    try {
      const element = await page.evaluate((sel) => {
        return document.querySelector(sel) !== null;
      }, selector);
      if (element) {
        loggedOutCount++;
        logger.log(`Found logout indicator: ${selector}`);
      }
    } catch (e) {
      // Selector might not be valid, continue
    }
  }

  // Additional URL-based validation
  const currentUrl = page.url();
  const isOnLoginPage =
    currentUrl.includes("login") ||
    currentUrl.includes("signin") ||
    currentUrl.includes("auth");

  logger.log(
    `Validation results: loggedIn=${loggedInCount}, loggedOut=${loggedOutCount}, onLoginPage=${isOnLoginPage}`
  );

  // Determine login status
  if (loggedInCount > 0 && loggedOutCount === 0 && !isOnLoginPage) {
    logger.success("Session validation: LOGGED IN");
    return { isLoggedIn: true, confidence: "high", indicators: loggedInCount };
  } else if (loggedOutCount > 0 || isOnLoginPage) {
    logger.warning("Session validation: LOGGED OUT");
    return {
      isLoggedIn: false,
      confidence: "high",
      indicators: loggedOutCount,
    };
  } else {
    logger.warning("Session validation: UNCERTAIN");
    return { isLoggedIn: false, confidence: "low", indicators: 0 };
  }
}

/**
 * Capture all browser storage data including IndexedDB
 */
export async function captureStorageData(page, logger) {
  logger.info("Capturing storage data...");

  const storageData = {
    cookies: [],
    localStorage: {},
    sessionStorage: {},
    indexedDB: {},
  };

  try {
    // Capture cookies
    storageData.cookies = await page.browserContext().cookies();
    logger.log(`Captured ${storageData.cookies.length} cookies`);
  } catch (e) {
    logger.error("Failed to capture cookies", e);
  }

  try {
    // Capture localStorage
    storageData.localStorage = await page.evaluate(() => {
      const data = {};
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        data[key] = localStorage.getItem(key);
      }
      return data;
    });
    logger.log(
      `Captured ${
        Object.keys(storageData.localStorage).length
      } localStorage items`
    );
  } catch (e) {
    logger.error("Failed to capture localStorage", e);
  }

  try {
    // Capture sessionStorage
    storageData.sessionStorage = await page.evaluate(() => {
      const data = {};
      for (let i = 0; i < sessionStorage.length; i++) {
        const key = sessionStorage.key(i);
        data[key] = sessionStorage.getItem(key);
      }
      return data;
    });
    logger.log(
      `Captured ${
        Object.keys(storageData.sessionStorage).length
      } sessionStorage items`
    );
  } catch (e) {
    logger.error("Failed to capture sessionStorage", e);
  }

  try {
    // Capture IndexedDB
    storageData.indexedDB = await page.evaluate(async () => {
      const databases = await indexedDB.databases();
      const dbData = {};

      for (const dbInfo of databases) {
        try {
          const db = await new Promise((resolve, reject) => {
            const request = indexedDB.open(dbInfo.name);
            request.onsuccess = () => resolve(request.result);
            request.onerror = () => reject(request.error);
          });

          dbData[dbInfo.name] = {
            version: db.version,
            objectStoreNames: Array.from(db.objectStoreNames),
          };

          db.close();
        } catch (e) {
          console.warn(`Failed to access IndexedDB ${dbInfo.name}:`, e);
        }
      }

      return dbData;
    });
    logger.log(
      `Captured ${
        Object.keys(storageData.indexedDB).length
      } IndexedDB databases`
    );
  } catch (e) {
    logger.error("Failed to capture IndexedDB", e);
  }

  return storageData;
}

/**
 * Restore storage data to browser
 */
export async function restoreStorageData(page, storageData, logger) {
  logger.info("Restoring storage data...");

  try {
    // Restore cookies
    if (storageData.cookies && storageData.cookies.length > 0) {
      await page.browserContext().addCookies(storageData.cookies);
      logger.log(`Restored ${storageData.cookies.length} cookies`);
    }
  } catch (e) {
    logger.error("Failed to restore cookies", e);
  }

  try {
    // Restore localStorage
    if (
      storageData.localStorage &&
      Object.keys(storageData.localStorage).length > 0
    ) {
      await page.evaluate((data) => {
        for (const [key, value] of Object.entries(data)) {
          localStorage.setItem(key, value);
        }
      }, storageData.localStorage);
      logger.log(
        `Restored ${
          Object.keys(storageData.localStorage).length
        } localStorage items`
      );
    }
  } catch (e) {
    logger.error("Failed to restore localStorage", e);
  }

  try {
    // Restore sessionStorage
    if (
      storageData.sessionStorage &&
      Object.keys(storageData.sessionStorage).length > 0
    ) {
      await page.evaluate((data) => {
        for (const [key, value] of Object.entries(data)) {
          sessionStorage.setItem(key, value);
        }
      }, storageData.sessionStorage);
      logger.log(
        `Restored ${
          Object.keys(storageData.sessionStorage).length
        } sessionStorage items`
      );
    }
  } catch (e) {
    logger.error("Failed to restore sessionStorage", e);
  }

  // Note: IndexedDB restoration is complex and would require platform-specific logic
  // For now, we'll log what we found but not attempt restoration
  if (storageData.indexedDB && Object.keys(storageData.indexedDB).length > 0) {
    logger.info(
      `Found ${
        Object.keys(storageData.indexedDB).length
      } IndexedDB databases (restoration not implemented)`
    );
  }
}

/**
 * Save storage data to files
 */
export function saveStorageData(storageData, sessionPath, logger) {
  logger.info(`Saving storage data to ${sessionPath}`);

  if (!fs.existsSync(sessionPath)) {
    fs.mkdirSync(sessionPath, { recursive: true });
  }

  const files = {
    cookies: path.join(sessionPath, "cookies.json"),
    localStorage: path.join(sessionPath, "localStorage.json"),
    sessionStorage: path.join(sessionPath, "sessionStorage.json"),
    indexedDB: path.join(sessionPath, "indexedDB.json"),
  };

  for (const [type, filePath] of Object.entries(files)) {
    try {
      fs.writeFileSync(filePath, JSON.stringify(storageData[type], null, 2));
      logger.log(`Saved ${type} to ${filePath}`);
    } catch (e) {
      logger.error(`Failed to save ${type}`, e);
    }
  }
}

/**
 * Load storage data from files
 */
export function loadStorageData(sessionPath, logger) {
  logger.info(`Loading storage data from ${sessionPath}`);

  const storageData = {
    cookies: [],
    localStorage: {},
    sessionStorage: {},
    indexedDB: {},
  };

  const files = {
    cookies: path.join(sessionPath, "cookies.json"),
    localStorage: path.join(sessionPath, "localStorage.json"),
    sessionStorage: path.join(sessionPath, "sessionStorage.json"),
    indexedDB: path.join(sessionPath, "indexedDB.json"),
  };

  for (const [type, filePath] of Object.entries(files)) {
    try {
      if (fs.existsSync(filePath)) {
        storageData[type] = JSON.parse(fs.readFileSync(filePath, "utf8"));
        logger.log(`Loaded ${type} from ${filePath}`);
      }
    } catch (e) {
      logger.error(`Failed to load ${type}`, e);
    }
  }

  return storageData;
}

/**
 * Test session persistence by navigating through demo URLs
 */
export async function testSessionPersistence(page, platform, logger) {
  logger.info(`Testing session persistence for ${platform}`);

  const config = PLATFORMS[platform];
  const results = [];

  for (const demo of config.demoUrls) {
    logger.info(`Testing ${demo.name}: ${demo.url}`);

    try {
      await page.goto(demo.url, { waitUntil: "networkidle2", timeout: 30000 });
      await page.waitForTimeout(2000); // Allow page to settle

      const validation = await validateSession(page, platform, logger);

      results.push({
        name: demo.name,
        url: demo.url,
        success: validation.isLoggedIn,
        confidence: validation.confidence,
        indicators: validation.indicators,
      });

      if (validation.isLoggedIn) {
        logger.success(`${demo.name}: Session persisted successfully`);
      } else {
        logger.warning(`${demo.name}: Session validation failed`);
      }
    } catch (e) {
      logger.error(`Failed to test ${demo.name}`, e);
      results.push({
        name: demo.name,
        url: demo.url,
        success: false,
        error: e.message,
      });
    }
  }

  return results;
}
