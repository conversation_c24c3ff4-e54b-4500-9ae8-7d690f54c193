import puppeteer from "puppeteer";
import fs from "fs";
import path from "path";
import readline from "readline";
import { fileURLToPath } from "url";
import { dirname } from "path";

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});

const prompt = (question) =>
  new Promise((resolve) => rl.question(question, resolve));

const PLATFORMS = {
  github: {
    name: "GitHub",
    loginUrl: "https://github.com/login",
    homeUrl: "https://github.com/",
    loginSelector: 'meta[name="user-login"]',
    demoUrls: [
      {
        url: "https://github.com/?tab=repositories",
        name: "Your Repositories",
      },
      { url: "https://github.com/explore", name: "Explore" },
    ],
  },
  google: {
    name: "Google",
    loginUrl: "https://www.google.com/",
    homeUrl: "https://www.google.com/",
    loginSelector: 'a[aria-label="Google Account: <EMAIL>"]',
    demoUrls: [
      { url: "https://mail.google.com/mail/u/0/#inbox", name: "Gmail" },
      { url: "https://calendar.google.com/", name: "Calendar" },
    ],
  },
  facebook: {
    name: "Facebook",
    loginUrl: "https://facebook.com/login",
    homeUrl: "https://facebook.com/",
    loginSelector: 'div[role="navigation"] image',
    demoUrls: [
      { url: "https://www.facebook.com/bookmarks/", name: "Bookmarks" },
      { url: "https://www.facebook.com/marketplace/", name: "Marketplace" },
    ],
  },
};

const getPlatformFromArgs = () => {
  const args = process.argv.slice(2);
  if (args.includes("--help") || args.includes("-h")) {
    console.log("\n=== Session Injection Script Help ===");
    console.log("Usage: node quick-session-inject.js [platform]");
    console.log("\nAvailable platforms:");
    Object.keys(PLATFORMS).forEach((platform) => {
      console.log(`  - ${platform}`);
    });
    console.log("\nExamples:");
    console.log("  node quick-session-inject.js github");
    console.log("  node quick-session-inject.js facebook");
    console.log(
      "\nIf no platform is specified, github will be used by default."
    );
    process.exit(0);
  }

  const platformArg = args.find((arg) => PLATFORMS[arg.toLowerCase()]);
  return platformArg ? platformArg.toLowerCase() : "github";
};

const PLATFORM_ID = getPlatformFromArgs();
const SESSION_NAME = PLATFORM_ID;
const SESSION_DIR = path.join(
  dirname(fileURLToPath(import.meta.url)),
  "sessions"
);
const SESSION_PATH = path.join(SESSION_DIR, SESSION_NAME);
const COOKIES_FILE = path.join(SESSION_PATH, "cookies.json");
const LOCALSTORAGE_FILE = path.join(SESSION_PATH, "localStorage.json");
const SESSIONSTORAGE_FILE = path.join(SESSION_PATH, "sessionStorage.json");

(async () => {
  try {
    console.log(
      `=== Quick Session Injection: ${PLATFORMS[PLATFORM_ID].name} ===`
    );

    const createNewSession = !fs.existsSync(COOKIES_FILE);

    if (createNewSession) {
      if (!fs.existsSync(SESSION_DIR)) {
        fs.mkdirSync(SESSION_DIR, { recursive: true });
      }
      if (!fs.existsSync(SESSION_PATH)) {
        fs.mkdirSync(SESSION_PATH);
      }

      const browser = await puppeteer.launch({
        headless: false,
        args: ["--remote-debugging-port=9222"],
        userDataDir: "/Users/<USER>/aalu",
      });
      const page = await browser.newPage();
      await page.goto(PLATFORMS[PLATFORM_ID].loginUrl);
      console.log("\n=== MANUAL ACTION REQUIRED ===");
      console.log(
        `Please log in to ${PLATFORMS[PLATFORM_ID].name} in the browser window.`
      );
      console.log("Complete ALL steps including 2FA/OTP if required.");

      await prompt(
        "\nPress Enter AFTER you've completely logged in (including 2FA if needed): "
      );
      await page.goto(PLATFORMS[PLATFORM_ID].homeUrl);

      const isLoggedIn = true;

      if (!isLoggedIn) {
        console.log(`Login verification failed. Please try again.`);
        await prompt("\nPress Enter to close the browser and exit: ");
        await browser.close();
        rl.close();
        return;
      }

      console.log("Login verified successfully! Saving session data...");

      const cookies = await page.browserContext().cookies();
      fs.writeFileSync(COOKIES_FILE, JSON.stringify(cookies, null, 2));
      try {
        const localStorageData = await page.evaluate(() => {
          const data = {};
          for (let i = 0; i < localStorage.length; i++) {
            const key = localStorage.key(i);
            data[key] = localStorage.getItem(key);
          }
          return data;
        });
        fs.writeFileSync(
          LOCALSTORAGE_FILE,
          JSON.stringify(localStorageData, null, 2)
        );
      } catch (e) {}

      // Save sessionStorage
      try {
        const sessionStorageData = await page.evaluate(() => {
          const data = {};
          for (let i = 0; i < sessionStorage.length; i++) {
            const key = sessionStorage.key(i);
            data[key] = sessionStorage.getItem(key);
          }
          return data;
        });
        fs.writeFileSync(
          SESSIONSTORAGE_FILE,
          JSON.stringify(sessionStorageData, null, 2)
        );
      } catch (e) {}

      try {
        const username = await page.evaluate((selector) => {
          const element = document.querySelector(selector);
          if (element) {
            return (
              element.getAttribute("content") ||
              element.getAttribute("alt") ||
              element.textContent ||
              "User"
            );
          }
          return "User";
        }, PLATFORMS[PLATFORM_ID].loginSelector);

        if (username) {
          fs.writeFileSync(path.join(SESSION_PATH, "username.txt"), username);
        }
      } catch (e) {}

      await page.screenshot({
        path: path.join(SESSION_PATH, "login_proof.png"),
      });

      console.log("\nSession created and saved successfully!");
      const keepOpen = await prompt(
        "\nDo you want to keep the browser open? (y/n): "
      );

      if (keepOpen.toLowerCase() !== "y") {
        await browser.close();
      }

      rl.close();
    } else {
      console.log(`Found existing session. Injecting into a new browser...`);
      if (!fs.existsSync(COOKIES_FILE)) {
        console.log("Error: Cookie file not found.");
        rl.close();
        return;
      }

      const cookies = JSON.parse(fs.readFileSync(COOKIES_FILE, "utf8"));

      const browser = await puppeteer.launch({
        headless: false,
        args: [
          "--remote-debugging-port=9222",
          "--auto-accept-this-tab-capture",
          "--remote-allow-origins=*",
          "--no-sandbox",
        ],
      });
      const page = await browser.newPage();
      await page.setCookie(...cookies);

      let localStorageData = {};
      if (fs.existsSync(LOCALSTORAGE_FILE)) {
        localStorageData = JSON.parse(
          fs.readFileSync(LOCALSTORAGE_FILE, "utf8")
        );
      }
      let sessionStorageData = {};
      if (fs.existsSync(SESSIONSTORAGE_FILE)) {
        sessionStorageData = JSON.parse(
          fs.readFileSync(SESSIONSTORAGE_FILE, "utf8")
        );
      }

      await page.goto(PLATFORMS[PLATFORM_ID].homeUrl);
      if (Object.keys(localStorageData).length > 0) {
        await page.evaluate((data) => {
          for (const [key, value] of Object.entries(data)) {
            localStorage.setItem(key, value);
          }
          return Object.keys(data).length;
        }, localStorageData);
      }

      if (Object.keys(sessionStorageData).length > 0) {
        await page.evaluate((data) => {
          for (const [key, value] of Object.entries(data)) {
            sessionStorage.setItem(key, value);
          }
          return Object.keys(data).length;
        }, sessionStorageData);
      }
      const isLoggedIn = await page.evaluate((selector) => {
        return document.querySelector(selector) !== null;
      }, PLATFORMS[PLATFORM_ID].loginSelector);

      if (isLoggedIn) {
        console.log("Session injection successful!");

        const showDemo = await prompt(
          "\nDo you want to see a demonstration of the session? (y/n): "
        );

        if (showDemo.toLowerCase() === "y") {
          for (const demoSite of PLATFORMS[PLATFORM_ID].demoUrls) {
            console.log(`Navigating to ${demoSite.name}...`);
            await page.goto(demoSite.url);
            await new Promise((resolve) => setTimeout(resolve, 2000));
          }
        }

        const keepOpen = await prompt(
          "Do you want to keep the browser open? (y/n): "
        );

        if (keepOpen.toLowerCase() !== "y") {
          await browser.close();
        }
      } else {
        console.log("Session injection failed. Cookies may have expired.");

        const createNew = await prompt(
          "Do you want to create a new session? (y/n): "
        );

        if (createNew.toLowerCase() === "y") {
          await browser.close();
          fs.unlinkSync(COOKIES_FILE);
          if (fs.existsSync(LOCALSTORAGE_FILE))
            fs.unlinkSync(LOCALSTORAGE_FILE);
          if (fs.existsSync(SESSIONSTORAGE_FILE))
            fs.unlinkSync(SESSIONSTORAGE_FILE);

          console.log(
            "Deleted old session files. Please run the script again to create a new session."
          );
        } else {
          await browser.close();
        }
      }
    }

    rl.close();
  } catch (error) {
    console.error("Error:", error);
    if (rl) rl.close();
  }
})();
