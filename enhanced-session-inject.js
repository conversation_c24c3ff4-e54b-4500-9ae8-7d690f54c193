/**
 * Enhanced Session Injection Script
 * Improved version with better validation, IndexedDB support, and comprehensive testing
 */

import puppeteer from "puppeteer";
import fs from "fs";
import path from "path";
import readline from "readline";
import { fileURLToPath } from "url";
import { dirname } from "path";
import {
  PLATFORMS,
  SessionLogger,
  validateSession,
  captureStorageData,
  restoreStorageData,
  saveStorageData,
  loadStorageData,
  testSessionPersistence,
} from "./session-utils.js";

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout,
});

const prompt = (question) =>
  new Promise((resolve) => rl.question(question, resolve));

const getPlatformFromArgs = () => {
  const args = process.argv.slice(2);
  if (args.includes("--help") || args.includes("-h")) {
    console.log("\n=== Enhanced Session Injection Script Help ===");
    console.log("Usage: node enhanced-session-inject.js [platform] [options]");
    console.log("\nAvailable platforms:");
    Object.keys(PLATFORMS).forEach((platform) => {
      console.log(`  - ${platform}`);
    });
    console.log("\nOptions:");
    console.log("  --verbose, -v    Enable verbose logging");
    console.log("  --test, -t       Run comprehensive session tests");
    console.log("  --force, -f      Force recreation of session");
    console.log("\nExamples:");
    console.log("  node enhanced-session-inject.js github");
    console.log("  node enhanced-session-inject.js google --verbose");
    console.log("  node enhanced-session-inject.js facebook --test");
    console.log(
      "\nIf no platform is specified, github will be used by default."
    );
    process.exit(0);
  }

  const platformArg = args.find((arg) => PLATFORMS[arg.toLowerCase()]);
  const verbose = args.includes("--verbose") || args.includes("-v");
  const test = args.includes("--test") || args.includes("-t");
  const force = args.includes("--force") || args.includes("-f");

  return {
    platform: platformArg ? platformArg.toLowerCase() : "github",
    verbose,
    test,
    force,
  };
};

const options = getPlatformFromArgs();
const PLATFORM_ID = options.platform;
const SESSION_NAME = PLATFORM_ID;
const SESSION_DIR = path.join(
  dirname(fileURLToPath(import.meta.url)),
  "sessions"
);
const SESSION_PATH = path.join(SESSION_DIR, SESSION_NAME);
const COOKIES_FILE = path.join(SESSION_PATH, "cookies.json");

// Initialize logger
const logger = new SessionLogger(PLATFORM_ID, options.verbose);

async function createNewSession() {
  logger.info("Creating new session...");

  if (!fs.existsSync(SESSION_DIR)) {
    fs.mkdirSync(SESSION_DIR, { recursive: true });
  }
  if (!fs.existsSync(SESSION_PATH)) {
    fs.mkdirSync(SESSION_PATH);
  }

  // const response = await fetch("http://localhost:9222/json/version");

  // const data = await response.json();
  // const { webSocketDebuggerUrl } = await data;

  const browser = await puppeteer.launch({
    headless: false,
    args: ["--remote-debugging-port=9222"],
  });

  const page = await browser.newPage();
  await page.goto(PLATFORMS[PLATFORM_ID].loginUrl);

  logger.info("=== MANUAL ACTION REQUIRED ===");
  logger.info(
    `Please log in to ${PLATFORMS[PLATFORM_ID].name} in the browser window.`
  );
  logger.info("Complete ALL steps including 2FA/OTP if required.");

  await prompt(
    "\nPress Enter AFTER you've completely logged in (including 2FA if needed): "
  );

  // Navigate to home page for validation
  await page.goto(PLATFORMS[PLATFORM_ID].homeUrl);

  // Validate login
  const validation = await validateSession(page, PLATFORM_ID, logger);

  if (!validation.isLoggedIn) {
    logger.error(`Login verification failed. Please try again.`);
    await prompt("\nPress Enter to close the browser and exit: ");
    await browser.close();
    rl.close();
    return false;
  }

  logger.success("Login verified successfully! Saving session data...");

  // Capture all storage data
  const storageData = await captureStorageData(page, logger);

  // Save to files
  saveStorageData(storageData, SESSION_PATH, logger);

  // Save username if possible
  try {
    const username = await page.evaluate((selectors) => {
      for (const selector of selectors) {
        const element = document.querySelector(selector);
        if (element) {
          return (
            element.getAttribute("content") ||
            element.getAttribute("alt") ||
            element.textContent ||
            "User"
          );
        }
      }
      return "User";
    }, PLATFORMS[PLATFORM_ID].loginSelectors);

    if (username && username !== "User") {
      fs.writeFileSync(path.join(SESSION_PATH, "username.txt"), username);
      logger.log(`Saved username: ${username}`);
    }
  } catch (e) {
    logger.warning("Could not extract username");
  }

  // Take screenshot
  await page.screenshot({
    path: path.join(SESSION_PATH, "login_proof.png"),
  });

  logger.success("Session created and saved successfully!");

  // Test session if requested
  if (options.test) {
    logger.info("Running session persistence tests...");
    const testResults = await testSessionPersistence(page, PLATFORM_ID, logger);

    // Save test results
    fs.writeFileSync(
      path.join(SESSION_PATH, "test_results.json"),
      JSON.stringify(testResults, null, 2)
    );

    logger.success(
      `Test results saved. Passed: ${
        testResults.filter((r) => r.success).length
      }/${testResults.length}`
    );
  }

  const keepOpen = await prompt(
    "\nDo you want to keep the browser open? (y/n): "
  );

  if (keepOpen.toLowerCase() !== "y") {
    await browser.close();
  }

  return true;
}

async function injectExistingSession() {
  logger.info("Injecting existing session...");

  if (!fs.existsSync(COOKIES_FILE)) {
    logger.error("Cookie file not found. Please create a session first.");
    return false;
  }

  // Load storage data
  const storageData = loadStorageData(SESSION_PATH, logger);

  const browser = await puppeteer.launch({
    headless: false,
    args: [
      "--remote-debugging-port=9222",
      "--auto-accept-this-tab-capture",
      "--remote-allow-origins=*",
      "--no-sandbox",
    ],
  });

  const page = await browser.newPage();

  // First, navigate to the domain to establish context
  await page.goto(PLATFORMS[PLATFORM_ID].homeUrl);

  // Restore storage data
  await restoreStorageData(page, storageData, logger);

  // Reload page to apply restored data
  await page.reload({ waitUntil: "networkidle2" });

  // Validate session
  const validation = await validateSession(page, PLATFORM_ID, logger);

  if (validation.isLoggedIn) {
    logger.success("Session injection successful!");

    // Run tests if requested
    if (options.test) {
      logger.info("Running session persistence tests...");
      const testResults = await testSessionPersistence(
        page,
        PLATFORM_ID,
        logger
      );

      // Save test results
      fs.writeFileSync(
        path.join(SESSION_PATH, "test_results.json"),
        JSON.stringify(testResults, null, 2)
      );

      logger.success(
        `Test results saved. Passed: ${
          testResults.filter((r) => r.success).length
        }/${testResults.length}`
      );

      // Show detailed results
      testResults.forEach((result) => {
        if (result.success) {
          logger.success(`✅ ${result.name}: Working`);
        } else {
          logger.warning(
            `❌ ${result.name}: Failed${
              result.error ? ` (${result.error})` : ""
            }`
          );
        }
      });
    } else {
      // Show demo if not testing
      const showDemo = await prompt(
        "\nDo you want to see a demonstration of the session? (y/n): "
      );

      if (showDemo.toLowerCase() === "y") {
        for (const demoSite of PLATFORMS[PLATFORM_ID].demoUrls) {
          logger.info(`Navigating to ${demoSite.name}...`);
          await page.goto(demoSite.url);
          await new Promise((resolve) => setTimeout(resolve, 2000));
        }
      }
    }

    const keepOpen = await prompt(
      "Do you want to keep the browser open? (y/n): "
    );

    if (keepOpen.toLowerCase() !== "y") {
      await browser.close();
    }

    return true;
  } else {
    logger.warning("Session injection failed. Cookies may have expired.");
    logger.info(
      `Validation confidence: ${validation.confidence}, indicators: ${validation.indicators}`
    );

    const createNew = await prompt(
      "Do you want to create a new session? (y/n): "
    );

    if (createNew.toLowerCase() === "y") {
      await browser.close();

      // Clean up old session files
      const filesToDelete = [
        path.join(SESSION_PATH, "cookies.json"),
        path.join(SESSION_PATH, "localStorage.json"),
        path.join(SESSION_PATH, "sessionStorage.json"),
        path.join(SESSION_PATH, "indexedDB.json"),
      ];

      filesToDelete.forEach((file) => {
        if (fs.existsSync(file)) {
          fs.unlinkSync(file);
          logger.log(`Deleted ${file}`);
        }
      });

      logger.info("Deleted old session files. Creating new session...");
      return await createNewSession();
    } else {
      await browser.close();
      return false;
    }
  }
}

// Main execution
(async () => {
  try {
    logger.success(
      `=== Enhanced Session Injection: ${PLATFORMS[PLATFORM_ID].name} ===`
    );
    logger.info(
      `Options: verbose=${options.verbose}, test=${options.test}, force=${options.force}`
    );

    const createNewSession_flag = !fs.existsSync(COOKIES_FILE) || options.force;

    if (createNewSession_flag) {
      if (options.force) {
        logger.info("Force flag detected, recreating session...");
      }
      const success = await createNewSession();
      if (!success) {
        logger.error("Failed to create session");
        process.exit(1);
      }
    } else {
      const success = await injectExistingSession();
      if (!success) {
        logger.error("Session injection failed");
        process.exit(1);
      }
    }

    logger.success("Script completed successfully!");
    rl.close();
  } catch (error) {
    logger.error("Unexpected error:", error);
    if (rl) rl.close();
    process.exit(1);
  }
})();
