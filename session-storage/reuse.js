// reuse_profile_check.js (ESM)
// Launch Chrome re-using the saved profile and test whether Google sees you as logged in.

import puppeteer from "puppeteer";

(async () => {
  const profileDir = "./saved-profile"; // must match setup_and_login.js
  try {
    // const res = await fetch("http://localhost:9222/json/version");
    // const { webSocketDebuggerUrl } = await res.json();
    const browser = await puppeteer.launch({
      headless: false,
      userDataDir: profileDir,
      args: ["--no-first-run", "--no-default-browser-check"],
    });

    const page = await browser.newPage();
    await page.goto("https://myaccount.google.com", {
      waitUntil: "networkidle2",
      timeout: 60000,
    });

    const currentUrl = page.url();
    console.log("Landing URL:", currentUrl);

    // Heuristic check for signed-in state: look for presence of account UI
    const isSignedIn = await page
      .evaluate(() => {
        // Many logged-in Google pages show email or profile image anchors.
        if (document.querySelector('img[alt*="Google Account"]')) return true;
        if (
          document.querySelector(
            'a[href*="SignOut"], a[href*="SignOutOptions"]'
          )
        )
          return true;
        // try to find obvious email text (heuristic)
        const bodyText = document.body ? document.body.innerText : "";
        if (bodyText && bodyText.includes("@")) return true;
        return false;
      })
      .catch(() => false);

    if (
      currentUrl.includes("signin") ||
      currentUrl.includes("ServiceLogin") ||
      !isSignedIn
    ) {
      console.log(
        "Result: The profile is NOT fully authenticated (Google asked to sign in)."
      );
    } else {
      console.log(
        "Result: The profile appears to be signed in and persisted across runs."
      );
    }

    // Print cookies count for debugging
    try {
      const client = await page.target().createCDPSession();
      const { cookies } = await client.send("Storage.getCookies");
      console.log(`Cookies visible in this context: ${cookies.length}`);
      const names = cookies.map((c) => c.name).slice(0, 30);
      console.log("Cookie sample:", names.join(", "));
    } catch (err) {
      console.warn("Could not fetch cookies via CDP:", err.message || err);
    }

    // Keep the browser open for manual inspection; close automatically if you prefer:
    // await browser.close();
  } catch (err) {
    console.error("Error in reuse_profile_check:", err);
    process.exitCode = 1;
  }
})();
