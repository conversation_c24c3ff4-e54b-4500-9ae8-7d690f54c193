// backup-session.mjs
import { execSync } from "child_process";
import fs from "fs";
import path from "path";
import os from "os";

const chromeDir =
  process.platform === "darwin"
    ? path.join(
        os.homedir(),
        "Library/Application Support/Google/Chrome/Default"
      )
    : process.platform === "win32"
    ? path.join(os.homedir(), "AppData/Local/Google/Chrome/User Data/Default")
    : path.join(os.homedir(), ".config/google-chrome/Default");

const projectDir = process.cwd();
const backupDir = path.join(projectDir, "saved-session");

const importantFiles = [
  "Cookies",
  "Login Data",
  "Local Storage",
  "Sessions",
  "Web Data",
  "Network Action Predictor",
  "History", // optional, but can matter for some flows
];

function isChromeRunning() {
  try {
    if (process.platform === "darwin") {
      execSync("pgrep -x Google\\ Chrome");
    } else if (process.platform === "win32") {
      execSync('tasklist /FI "IMAGENAME eq chrome.exe"');
    } else {
      execSync("pgrep chrome");
    }
    return true;
  } catch {
    return false;
  }
}

if (isChromeRunning()) {
  console.error("❌ Chrome must be closed before backing up session files.");
  process.exit(1);
}

if (!fs.existsSync(backupDir)) {
  fs.mkdirSync(backupDir, { recursive: true });
}

importantFiles.forEach((file) => {
  const src = path.join(chromeDir, file);
  if (fs.existsSync(src)) {
    const dest = path.join(backupDir, file);
    fs.cpSync(src, dest, { recursive: true });
    console.log(`✅ Backed up ${file}`);
  } else {
    console.warn(`⚠️  Skipped missing file: ${file}`);
  }
});

console.log(`\n🎉 Session backup complete at: ${backupDir}`);
