// setup_and_login.js (ESM)
// Launch Chrome with a userDataDir and wait for you to sign in to Google.

import puppeteer from "puppeteer";
import readline from "readline/promises";
import { stdin as input, stdout as output } from "node:process";
import fs from "fs";

(async () => {
  const profileDir = "./saved-profile"; // change if you want another path
  try {
    const browser = await puppeteer.launch({
      headless: false,
      userDataDir: profileDir,
      args: ["--no-first-run", "--no-default-browser-check"],
      // If you want to use system Chrome, add executablePath: '/path/to/chrome'
    });

    const page = await browser.newPage();
    console.log("Opening https://myaccount.google.com ...");
    await page.goto("https://myaccount.google.com", {
      waitUntil: "networkidle2",
    });

    console.log("\n--- Action required ---");
    console.log(
      "Please sign in to Google in the opened browser window (if needed)."
    );
    console.log(
      "When you are done signing in and can see your account, come back to this terminal and press Enter.\n"
    );

    const rl = readline.createInterface({ input, output });
    await rl.question("Press Enter after you finish signing in... ");
    rl.close();

    // Optional: snapshot cookies for verification
    const client = await page.target().createCDPSession();
    const { cookies } = await client.send("Storage.getCookies");
    console.log(`Saved ${cookies.length} cookies from this profile.`);
    console.log("Profile folder saved at:", profileDir);

    await browser.close();
    console.log(
      "Browser closed. You can now run reuse_profile_check.js to confirm reuse."
    );
  } catch (err) {
    console.error("Error in setup_and_login:", err);
    process.exitCode = 1;
  }
})();
